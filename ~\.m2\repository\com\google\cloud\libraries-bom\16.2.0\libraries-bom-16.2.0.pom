<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
                             http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>com.google.cloud</groupId>
  <artifactId>libraries-bom</artifactId>
  <version>16.2.0</version>
  <packaging>pom</packaging>

  <name>Google Cloud Platform Supported Libraries</name>
  <description>A compatible set of Google Cloud open source libraries.</description>
  <url>https://github.com/GoogleCloudPlatform/cloud-opensource-java/wiki/The-Google-Cloud-Platform-Libraries-BOM</url>
  <organization>
    <name>Google LLC</name>
    <url>https://cloud.google.com</url>
  </organization>
  <inceptionYear>2019</inceptionYear>
  <developers>
    <developer>
      <name><PERSON><PERSON></name>
    </developer>
  </developers>

  <issueManagement>
    <url>https://github.com/GoogleCloudPlatform/cloud-opensource-java/issues</url>
  </issueManagement>
  <scm>
    <connection>scm:git:**************:GoogleCloudPlatform/cloud-opensource-java.git</connection>
    <developerConnection>scm:git:**************:GoogleCloudPlatform/cloud-opensource-java.git
    </developerConnection>
    <url>https://github.com/GoogleCloudPlatform/cloud-opensource-java/boms/cloud-oss-bom</url>
    <tag>HEAD</tag>
  </scm>

  <licenses>
    <license>
      <name>The Apache License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
    </license>
  </licenses>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <guava.version>30.1-android</guava.version>
    <google.cloud.java.version>0.146.0</google.cloud.java.version>
    <io.grpc.version>1.34.1</io.grpc.version>
    <protobuf.version>3.14.0</protobuf.version>
    <http.version>1.38.0</http.version>
    <!-- We don't use gax-bom because it includes the artifacts with 'testlib' classifier.
        When updating gax.version, update gax.httpjson.version too. -->
    <gax.version>1.60.1</gax.version>
    <gax.httpjson.version>0.77.1</gax.httpjson.version>
    <common.protos.version>2.0.1</common.protos.version>
    <iam.protos.version>1.0.4</iam.protos.version>
  </properties>

  <distributionManagement>
    <snapshotRepository>
      <id>sonatype-nexus-snapshots</id>
      <name>Sonatype Nexus Snapshots</name>
      <url>https://oss.sonatype.org/content/repositories/snapshots/</url>
    </snapshotRepository>
  </distributionManagement>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.google.guava</groupId>
        <artifactId>guava-bom</artifactId>
        <version>${guava.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>

      <!-- protobufs from https://github.com/protocolbuffers/protobuf/tree/master/java -->
      <dependency>
        <groupId>com.google.protobuf</groupId>
        <artifactId>protobuf-bom</artifactId>
        <version>${protobuf.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>

      <!-- google-http-java-client from https://github.com/googleapis/google-http-java-client -->
     <dependency>
       <groupId>com.google.http-client</groupId>
       <artifactId>google-http-client</artifactId>
       <version>${http.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.http-client</groupId>
        <artifactId>google-http-client-android</artifactId>
        <version>${http.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.http-client</groupId>
        <artifactId>google-http-client-apache-v2</artifactId>
        <version>${http.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.http-client</groupId>
        <artifactId>google-http-client-appengine</artifactId>
        <version>${http.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.http-client</groupId>
        <artifactId>google-http-client-gson</artifactId>
        <version>${http.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.http-client</groupId>
        <artifactId>google-http-client-jackson2</artifactId>
        <version>${http.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.http-client</groupId>
        <artifactId>google-http-client-protobuf</artifactId>
        <version>${http.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.http-client</groupId>
        <artifactId>google-http-client-test</artifactId>
        <version>${http.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.http-client</groupId>
        <artifactId>google-http-client-xml</artifactId>
        <version>${http.version}</version>
      </dependency>

      <!-- GRPC; specifically https://github.com/grpc/grpc-java -->
      <dependency>
        <groupId>io.grpc</groupId>
        <artifactId>grpc-alts</artifactId>
        <version>${io.grpc.version}</version>
      </dependency>
      <dependency>
        <groupId>io.grpc</groupId>
        <artifactId>grpc-api</artifactId>
        <version>${io.grpc.version}</version>
      </dependency>
      <dependency>
        <groupId>io.grpc</groupId>
        <artifactId>grpc-auth</artifactId>
        <version>${io.grpc.version}</version>
      </dependency>
      <dependency>
        <groupId>io.grpc</groupId>
        <artifactId>grpc-context</artifactId>
        <version>${io.grpc.version}</version>
      </dependency>
      <dependency>
        <groupId>io.grpc</groupId>
        <artifactId>grpc-core</artifactId>
        <version>${io.grpc.version}</version>
      </dependency>
      <dependency>
        <groupId>io.grpc</groupId>
        <artifactId>grpc-grpclb</artifactId>
        <version>${io.grpc.version}</version>
      </dependency>
      <dependency>
        <groupId>io.grpc</groupId>
        <artifactId>grpc-netty</artifactId>
        <version>${io.grpc.version}</version>
      </dependency>
      <dependency>
        <groupId>io.grpc</groupId>
        <artifactId>grpc-netty-shaded</artifactId>
        <version>${io.grpc.version}</version>
      </dependency>
      <dependency>
        <groupId>io.grpc</groupId>
        <artifactId>grpc-okhttp</artifactId>
        <version>${io.grpc.version}</version>
      </dependency>
      <dependency>
        <groupId>io.grpc</groupId>
        <artifactId>grpc-protobuf</artifactId>
        <version>${io.grpc.version}</version>
      </dependency>
      <dependency>
        <groupId>io.grpc</groupId>
        <artifactId>grpc-protobuf-lite</artifactId>
        <version>${io.grpc.version}</version>
      </dependency>
      <dependency>
        <groupId>io.grpc</groupId>
        <artifactId>grpc-services</artifactId>
        <version>${io.grpc.version}</version>
      </dependency>
      <dependency>
        <groupId>io.grpc</groupId>
        <artifactId>grpc-stub</artifactId>
        <version>${io.grpc.version}</version>
      </dependency>
      <dependency>
        <groupId>io.grpc</groupId>
        <artifactId>grpc-testing</artifactId>
        <version>${io.grpc.version}</version>
      </dependency>

      <!-- google-cloud-java from https://github.com/googleapis/google-cloud-java -->
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-bom</artifactId>
        <version>${google.cloud.java.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>

      <dependency>
        <groupId>com.google.api</groupId>
        <artifactId>api-common</artifactId>
         <version>1.10.1</version>
       </dependency>
       <dependency>
         <groupId>com.google.api</groupId>
         <artifactId>gax</artifactId>
         <version>${gax.version}</version>
       </dependency>
       <dependency>
         <groupId>com.google.api</groupId>
         <artifactId>gax-grpc</artifactId>
         <version>${gax.version}</version>
       </dependency>
       <dependency>
         <groupId>com.google.api</groupId>
         <artifactId>gax-httpjson</artifactId>
         <version>${gax.httpjson.version}</version>
       </dependency>
       <dependency>
         <groupId>com.google.auth</groupId>
         <artifactId>google-auth-library-bom</artifactId>
         <version>0.22.1</version>
         <type>pom</type>
         <scope>import</scope>
       </dependency>
       <dependency>
         <groupId>com.google.cloud</groupId>
         <artifactId>google-cloud-core-bom</artifactId>
         <version>1.94.0</version>
         <type>pom</type>
         <scope>import</scope>
       </dependency>
      <dependency>
         <groupId>com.google.api.grpc</groupId>
         <artifactId>proto-google-common-protos</artifactId>
         <version>${common.protos.version}</version>
       </dependency>
       <dependency>
         <groupId>com.google.api.grpc</groupId>
         <artifactId>grpc-google-common-protos</artifactId>
         <version>${common.protos.version}</version>
       </dependency>
       <dependency>
         <groupId>com.google.api.grpc</groupId>
         <artifactId>proto-google-iam-v1</artifactId>
         <version>${iam.protos.version}</version>
       </dependency>
       <dependency>
         <groupId>com.google.api.grpc</groupId>
         <artifactId>grpc-google-iam-v1</artifactId>
         <version>${iam.protos.version}</version>
       </dependency>

    </dependencies>
  </dependencyManagement>

</project>
