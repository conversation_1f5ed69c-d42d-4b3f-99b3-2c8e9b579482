<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.sonatype.oss</groupId>
        <artifactId>oss-parent</artifactId>
        <version>7</version>
    </parent>

    <groupId>org.skyscreamer</groupId>
    <artifactId>jsonassert</artifactId>
    <version>1.5.0</version>
    <packaging>jar</packaging>

    <name>JSONassert</name>
    <description>A library to develop RESTful but flexible APIs</description>
    <url>https://github.com/skyscreamer/JSONassert</url>

    <licenses>
        <license>
            <name>The Apache Software License, Version 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution>
        </license>
    </licenses>
    <scm>
        <connection>scm:git:**************:skyscreamer/JSONassert.git</connection>
        <developerConnection>scm:git:**************:skyscreamer/JSONassert.git</developerConnection>
        <url>**************:skyscreamer/JSONassert.git</url>
    </scm>
    <developers>
        <developer>
            <id>carterpage</id>
            <name>Carter Page</name>
            <email><EMAIL></email>
        </developer>
        <developer>
            <id>cepage</id>
            <name>Corby Page</name>
            <email><EMAIL></email>
        </developer>
        <developer>
            <id>sduskis</id>
            <name>Solomon Duskis</name>
            <email><EMAIL></email>
        </developer>
    </developers>

    <!-- For JSONAssert -->
    <dependencies>
        <dependency>
            <groupId>com.vaadin.external.google</groupId>
            <artifactId>android-json</artifactId>
            <version>0.0.20131108.vaadin1</version>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.10</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>2.3.1</version>
                <configuration>
                    <source>1.6</source>
                    <target>1.6</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-site-plugin</artifactId>
                <version>3.3</version>
                <configuration>
                    <reportPlugins>
                        <plugin>
                            <groupId>org.codehaus.mojo</groupId>
                            <artifactId>cobertura-maven-plugin</artifactId>
                            <version>2.5.1</version>
                        </plugin>
                    </reportPlugins>
                </configuration>
            </plugin>
        </plugins>
        <extensions>
            <extension>
                <groupId>org.apache.maven.scm</groupId>
                <artifactId>maven-scm-provider-gitexe</artifactId>
                <version>1.3</version>
            </extension>
            <extension>
                <groupId>org.apache.maven.scm</groupId>
                <artifactId>maven-scm-manager-plexus</artifactId>
                <version>1.3</version>
            </extension>
            <extension>
                <groupId>org.kathrynhuxtable.maven.wagon</groupId>
                <artifactId>wagon-gitsite</artifactId>
                <version>0.3.1</version>
            </extension>
        </extensions>
    </build>

    <distributionManagement>
        <site>
            <id>github-project-site</id>
            <url>gitsite:**************/skyscreamer/JSONassert.git</url>
        </site>
    </distributionManagement>

    <profiles>
        <profile>
            <id>release-sign-artifacts</id>
            <activation>
                <property>
                    <name>performRelease</name>
                    <value>true</value>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <version>1.1</version>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

</project>
