<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<parent>
		<groupId>org.sonatype.oss</groupId>
		<artifactId>oss-parent</artifactId>
		<version>2</version>
	</parent>

	<modelVersion>4.0.0</modelVersion>
	<groupId>com.googlecode.xmemcached</groupId>
	<artifactId>xmemcached</artifactId>
	<version>1.3.5</version>
	<name>xmemcached</name>
	<description>Extreme performance modern memcached client for java</description>
	<url>http://code.google.com/p/xmemcached/</url>
	<packaging>jar</packaging>
	<developers>
		<developer>
			<name>dennis zhuang</name>
			<url>http://www.blogjava.net/killme2008/</url>
			<timezone>8</timezone>
		</developer>
	</developers>

	<licenses>
		<license>
			<name>Apache License, Version 2.0</name>
			<url>http://www.apache.org/licenses/LICENSE-2.0</url>
		</license>
	</licenses>

	<scm>
		<connection>scm:git:**************:killme2008/xmemcached.git</connection>
		<developerConnection>scm:git:**************:killme2008/xmemcached.git</developerConnection>
		<url>**************:killme2008/xmemcached.git</url>
	</scm>

	<repositories>
		<repository>
			<id>java.net</id>
			<url>http://download.java.net/maven/2/</url>
			<releases>
				<enabled>true</enabled>
			</releases>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</repository>
		<repository>
			<id>hibernate-memcached</id>
			<name>hibernate-memcached</name>
			<url>http://raykrueger.googlecode.com/svn/repository</url>
		</repository>
	</repositories>
	
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-gpg-plugin</artifactId>
				<executions>
					<execution>
						<id>sign-artifacts</id>
						<phase>verify</phase>
						<goals>
							<goal>sign</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-javadoc-plugin</artifactId>
				<executions>
					<execution>
						<id>attach-javadoc</id>
						<phase>verify</phase>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>1.5</source>
					<target>1.5</target>
					<encoding>UTF-8</encoding>
				</configuration>
			</plugin>
			<plugin>
				<artifactId>maven-assembly-plugin</artifactId>
				<configuration>
					<descriptors>
						<descriptor>src/assemble/src.xml</descriptor>
						<descriptor>src/assemble/distribution.xml</descriptor>
					</descriptors>
				</configuration>
			</plugin>
		</plugins>
	</build>
	<dependencies>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>4.4</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
			<version>1.5.6</version>
		</dependency>
		<dependency>
			<groupId>log4j</groupId>
			<artifactId>log4j</artifactId>
			<version>1.2.16</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-log4j12</artifactId>
			<version>1.5.6</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring</artifactId>
			<version>2.5</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.googlecode</groupId>
			<artifactId>hibernate-memcached</artifactId>
			<version>1.2</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.easymock</groupId>
			<artifactId>easymock</artifactId>
			<version>2.4</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.easymock</groupId>
			<artifactId>easymockclassextension</artifactId>
			<version>2.4</version>
			<scope>test</scope>
		</dependency>
	</dependencies>
	<reporting>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-javadoc-plugin</artifactId>
				<configuration>
					<encoding>utf-8</encoding>
					<charset>utf-8</charset>
					<locale>en_US</locale>
					<excludePackageNames>net.rubyeye.xmemcached.test</excludePackageNames>
				</configuration>
			</plugin>
		</plugins>
	</reporting>
</project>