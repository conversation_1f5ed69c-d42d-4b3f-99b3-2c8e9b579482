<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">  
  <modelVersion>4.0.0</modelVersion>  
  <groupId>com.howbuy.common</groupId>  
  <artifactId>common-facade</artifactId>  
  <name>common-facade</name>  
  <version>3.5.7-RELEASE</version>
  <packaging>jar</packaging>  
  <properties> 
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>  
    <maven.compiler.encoding>UTF-8</maven.compiler.encoding>  
    <java.version>1.6</java.version> 
  </properties>  
  <build> 
    <plugins> 
      <plugin> 
        <groupId>org.apache.maven.plugins</groupId>  
        <artifactId>maven-compiler-plugin</artifactId>  
        <version>2.3.2</version>  
        <configuration> 
          <source>${java.version}</source>  
          <target>${java.version}</target>  
          <encoding>UTF-8</encoding> 
        </configuration> 
      </plugin>  
      <plugin> 
        <groupId>org.apache.maven.plugins</groupId>  
        <artifactId>maven-surefire-plugin</artifactId>  
        <version>2.5</version>  
        <configuration> 
          <skipTests>true</skipTests> 
        </configuration> 
      </plugin>
      <plugin>
		<groupId>org.apache.maven.plugins</groupId>
		<artifactId>maven-source-plugin</artifactId>
		<version>2.4</version>
		<executions>
			<execution>
				<id>attach-source</id>
				<phase>install</phase>
				<goals>
					<goal>jar-no-fork</goal>
				</goals>
			</execution>
		</executions>
	</plugin>
    </plugins> 
  </build>
    <distributionManagement>
        <repository>
            <id>releases</id>
            <name>nexus releases respository</name>
            <url>http://mvn.intelnal.howbuy.com/nexus/content/repositories/releases/</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>nexus snapshots respository</name>
            <url>http://mvn.intelnal.howbuy.com/nexus/content/repositories/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
  <dependencies /> 
</project>