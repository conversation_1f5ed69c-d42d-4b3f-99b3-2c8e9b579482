<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <artifactId>howbuy-commons-validator</artifactId>
  <name>howbuy-commons-validator</name>
  <groupId>com.howbuy.commons.validator</groupId>
  <version>1.0.0-SNAPSHOT</version>
  <dependencies>
  	<dependency>
			<groupId>net.sf.oval</groupId>
			<artifactId>oval</artifactId>
			<version>1.84</version>
		</dependency>
  </dependencies>
  <distributionManagement>
		<repository>
			<id>howbuy-releases</id>
			<name>howbuy-releases</name>
			<url>http://mvn.intelnal.howbuy.com/nexus/content/repositories/releases/</url>
		</repository>
		<snapshotRepository>
			<id>howbuy-snapshots</id>
			<name>howbuy-snapshots</name>
			<url>http://mvn.intelnal.howbuy.com/nexus/content/repositories/snapshots/</url>
		</snapshotRepository>
	</distributionManagement>
	<repositories>
		<repository>
			<releases>
				<enabled>true</enabled>
			</releases>
			<snapshots>
				<enabled>true</enabled>
				<updatePolicy>interval:10</updatePolicy>
			</snapshots>
			<id>howbuy-nexus</id>
			<name>howbuy-nexus</name>
			<url>http://mvn.intelnal.howbuy.com/nexus/content/groups/public/</url>
		</repository>
		
		<repository>
			<releases>
				<enabled>true</enabled>
			</releases>
			<snapshots>
				<enabled>true</enabled>
				<updatePolicy>interval:10</updatePolicy>
			</snapshots>
			<id>it-howbuy-nexus</id>
			<name>howbuy-nexus nexus</name>
			<url>http://mvn.intelnal.howbuy.com/nexus/content/groups/public/</url>
		</repository>
		
		<repository>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
			<id>central</id>
			<name>Central Repository</name>
			<url>http://repo.maven.apache.org/maven2</url>
		</repository>
	</repositories>
</project>