<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  
    <parent>
        <groupId>org.sonatype.oss</groupId>
        <artifactId>oss-parent</artifactId>
        <version>7</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <inceptionYear>2012</inceptionYear>
    <groupId>com.alibaba.rocketmq</groupId>
    <artifactId>rocketmq-all</artifactId>
    <version>3.2.6</version>
    <packaging>pom</packaging>
    <name>rocketmq-all ${project.version}</name>
    <url>https://github.com/alibaba/rocketmq</url>
    <description>https://github.com/alibaba/RocketMQ/blob/develop/README.md</description>

    <prerequisites>
        <maven>2.2.1</maven>
    </prerequisites>

    <modules>
        <module>rocketmq-client</module>
        <module>rocketmq-common</module>
        <module>rocketmq-broker</module>
        <module>rocketmq-tools</module>
        <module>rocketmq-store</module>
        <module>rocketmq-namesrv</module>
        <module>rocketmq-remoting</module>
        <module>rocketmq-example</module>
        <module>rocketmq-filtersrv</module>
        <module>rocketmq-srvutil</module>
    </modules>

    <developers>
        <developer>
            <name>vintagewang</name>
            <url>https://github.com/vintagewang</url>
            <email><EMAIL></email>
            <timezone>8</timezone>
        </developer>
        <developer>
            <name>manhong</name>
            <url>https://github.com/YangJodie</url>
            <email><EMAIL></email>
            <timezone>8</timezone>
        </developer>
        <developer>
            <name>allenzhu</name>
            <url>https://github.com/allenzhu</url>
            <email><EMAIL></email>
            <timezone>8</timezone>
        </developer>
        <developer>
            <id>Von Gosling</id>
            <name>Von Gosling</name>
            <email><EMAIL></email>
            <url>https://github.com/vongosling</url>
            <timezone>+8</timezone>
        </developer>
    </developers>

    <licenses>
        <license>
            <name>Apache License, Version 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0</url>
        </license>
    </licenses>

    <scm>
        <url>**************************:middleware/rocketmq.git</url>
        <connection>scm:git:**************************:middleware/rocketmq.git</connection>
        <developerConnection>scm:git:**************************:middleware/rocketmq.git</developerConnection>
    </scm>

    <distributionManagement>
        <repository>
            <id>sonatype-nexus-staging</id>
            <name>sonatype staging</name>
            <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
        </repository>
    </distributionManagement>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <!--maven properties -->
        <maven.test.skip>true</maven.test.skip>
        <maven.jdoc.skip>false</maven.jdoc.skip>
        <downloadSources>true</downloadSources>
        <!-- compiler settings properties -->
        <java_source_version>1.6</java_source_version>
        <java_target_version>1.6</java_target_version>
        <file_encoding>UTF-8</file_encoding>
    </properties>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <version>2.1</version>
            </plugin>
            <plugin>
                <groupId>com.github.vongosling</groupId>
                <artifactId>dependency-mediator-maven-plugin</artifactId>
                <version>1.0.2</version>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>clirr-maven-plugin</artifactId>
                <version>2.6.1</version>
            </plugin>
            <plugin>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>1.4</version>
                <executions>
                    <execution>
                        <id>enforce-ban-circular-dependencies</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <rules>
                        <banCircularDependencies />
                    </rules>
                    <fail>true</fail>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>extra-enforcer-rules</artifactId>
                        <version>1.0-beta-2</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.2</version>
                <configuration>
                    <source>${java_source_version}</source>
                    <target>${java_target_version}</target>
                    <encoding>${file_encoding}</encoding>
                    <showDeprecation>true</showDeprecation>
                    <showWarnings>true</showWarnings>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-eclipse-plugin</artifactId>
                <version>2.9</version>
                <configuration>
                    <downloadSources>true</downloadSources>
                    <downloadJavadocs>false</downloadJavadocs>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.18.1</version>
                <configuration>
                    <skip>${maven.test.skip}</skip>
                    <argLine>-Xms512m -Xmx1024m</argLine>
                    <forkMode>once</forkMode>
                    <includes>
                        <include>**/*Test.java</include>
                    </includes>
                    <excludes>
                        <exclude>com/alibaba/rocketmq/remoting/ExceptionTest.java</exclude>
                        <exclude>com/alibaba/rocketmq/remoting/SyncInvokeTest.java</exclude>
                        <exclude>com/alibaba/rocketmq/remoting/NettyIdleTest.java</exclude>
                        <exclude>com/alibaba/rocketmq/remoting/NettyConnectionTest.java</exclude>
                        <exclude>com/alibaba/rocketmq/common/filter/PolishExprTest.java</exclude>
                        <exclude>com/alibaba/rocketmq/common/protocol/MQProtosHelperTest.java</exclude>
                        <exclude>
                            com/alibaba/rocketmq/client/consumer/loadbalance/AllocateMessageQueueAveragelyTest.java
                        </exclude>
                        <exclude>com/alibaba/rocketmq/store/RecoverTest.java</exclude>
                        <exclude>com/alibaba/rocketmq/broker/api/SendMessageTest.java</exclude>
                        <exclude>com/alibaba/rocketmq/test/integration/*/*.java</exclude>
                        <exclude>com/alibaba/rocketmq/test/integration/BaseTest.java</exclude>
                        <exclude>com/alibaba/rocketmq/test/*/*.java</exclude>
                        <exclude>com/alibaba/rocketmq/test/BaseTest.java</exclude>
                    </excludes>
                </configuration>
            </plugin>


            <plugin>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>2.5.3</version>
                <configuration>
                    <finalName>alibaba-rocketmq-${project.version}</finalName>
                    <descriptors>
                        <descriptor>release.xml</descriptor>
                    </descriptors>
                </configuration>
            </plugin>

            <!--
                        <plugin>
                            <artifactId>maven-assembly-plugin</artifactId>
                            <configuration>
                                <finalName>alibaba-rocketmq-client-java-${project.version}</finalName>
                                <descriptors>
                                    <descriptor>release-client.xml</descriptor>
                                </descriptors>
                            </configuration>
                        </plugin>
            -->


            <plugin>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>2.10.1</version>
                <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <skip>${maven.jdoc.skip}</skip>
                    <encoding>${file_encoding}</encoding>
                    <charset>${file_encoding}</charset>
                    <doclet>org.jboss.apiviz.APIviz</doclet>
                    <docletArtifact>
                        <groupId>org.jboss.apiviz</groupId>
                        <artifactId>apiviz</artifactId>
                        <version>1.3.0.GA</version>
                    </docletArtifact>
                    <useStandardDocletOptions>true</useStandardDocletOptions>
                    <breakiterator>true</breakiterator>
                    <version>true</version>
                    <author>true</author>
                    <keywords>true</keywords>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-source-plugin</artifactId>
                <version>2.4</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>clirr-maven-plugin</artifactId>
                <version>2.6.1</version>
            </plugin>
        </plugins>

        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
            </resource>
        </resources>
    </build>

    <profiles>
        <profile>
            <id>release-sign-artifacts</id>
            <activation>
                <property>
                    <name>performRelease</name>
                    <value>true</value>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <version>1.6</version>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>rocketmq-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>rocketmq-broker</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>rocketmq-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>rocketmq-store</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>rocketmq-namesrv</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>rocketmq-tools</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>rocketmq-remoting</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>rocketmq-qatest</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>rocketmq-filtersrv</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>rocketmq-srvutil</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>4.11</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>1.7.5</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>1.0.13</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-core</artifactId>
                <version>1.0.13</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>2.4</version>
            </dependency>
            <dependency>
                <groupId>commons-cli</groupId>
                <artifactId>commons-cli</artifactId>
                <version>1.2</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-all</artifactId>
                <version>4.0.25.Final</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.3</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>5.1.31</version>
            </dependency>
            <dependency>
                <groupId>org.apache.derby</groupId>
                <artifactId>derby</artifactId>
                <version>10.10.2.0</version>
            </dependency>
            <dependency>
                <groupId>jboss</groupId>
                <artifactId>javassist</artifactId>
                <version>3.7.ga</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
