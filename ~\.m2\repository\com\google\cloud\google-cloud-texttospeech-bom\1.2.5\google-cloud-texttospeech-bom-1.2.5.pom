<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.cloud</groupId>
  <artifactId>google-cloud-texttospeech-bom</artifactId>
  <version>1.2.5</version><!-- {x-version-update:google-cloud-texttospeech:current} -->
  <packaging>pom</packaging>
  <parent>
    <groupId>com.google.cloud</groupId>
    <artifactId>google-cloud-shared-config</artifactId>
    <version>0.9.4</version>
  </parent>

  <name>Google Cloud texttospeech BOM</name>
  <url>https://github.com/googleapis/java-texttospeech</url>
  <description>
    BOM for Google Cloud Text-to-Speech
  </description>

  <organization>
    <name>Google LLC</name>
  </organization>

  <developers>
    <developer>
      <id>chingor13</id>
      <name>Jeff Ching</name>
      <email><EMAIL></email>
      <organization>Google LLC</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
  </developers>

  <scm>
    <connection>scm:git:https://github.com/googleapis/java-texttospeech.git</connection>
    <developerConnection>scm:git:**************:googleapis/java-texttospeech.git</developerConnection>
    <url>https://github.com/googleapis/java-texttospeech</url>
  </scm>

  <distributionManagement>
    <snapshotRepository>
      <id>sonatype-nexus-snapshots</id>
      <url>https://oss.sonatype.org/content/repositories/snapshots</url>
    </snapshotRepository>
    <repository>
      <id>sonatype-nexus-staging</id>
      <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
  </distributionManagement>

  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <dependencyManagement>
    <dependencies>

      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-texttospeech</artifactId>
        <version>1.2.5</version><!-- {x-version-update:google-cloud-texttospeech:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-texttospeech-v1beta1</artifactId>
        <version>0.85.5</version><!-- {x-version-update:grpc-google-cloud-texttospeech-v1beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-texttospeech-v1</artifactId>
        <version>1.2.5</version><!-- {x-version-update:grpc-google-cloud-texttospeech-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-texttospeech-v1</artifactId>
        <version>1.2.5</version><!-- {x-version-update:proto-google-cloud-texttospeech-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-texttospeech-v1beta1</artifactId>
        <version>0.85.5</version><!-- {x-version-update:proto-google-cloud-texttospeech-v1beta1:current} -->
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <configuration>
          <skip>true</skip>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>