<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <parent>
    <artifactId>assertj-parent-pom</artifactId>
    <groupId>org.assertj</groupId>
    <version>2.2.7</version>
    <relativePath>pom.xml</relativePath>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <artifactId>assertj-core</artifactId>
  <name>AssertJ fluent assertions</name>
  <version>3.16.1</version>
  <description>Rich and fluent assertions for testing for Java</description>
  <issueManagement>
    <system>github</system>
    <url>https://github.com/joel-costigliola/assertj-core/issues</url>
  </issueManagement>
  <mailingLists>
    <mailingList>
      <name>AssertJ Group</name>
      <subscribe>http://groups.google.com/group/assertj</subscribe>
      <unsubscribe>http://groups.google.com/group/assertj</unsubscribe>
      <post>http://groups.google.com/group/assertj</post>
    </mailingList>
  </mailingLists>
  <scm>
    <connection>scm:git:**************:joel-costigliola/assertj-core.git</connection>
    <developerConnection>scm:git:**************:joel-costigliola/assertj-core.git</developerConnection>
    <tag>assertj-core-3.16.1</tag>
    <url>**************:joel-costigliola/assertj-core</url>
  </scm>
  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>net.alchim31.maven</groupId>
          <artifactId>yuicompressor-maven-plugin</artifactId>
          <version>1.5.1</version>
        </plugin>
        <plugin>
          <groupId>org.eclipse.m2e</groupId>
          <artifactId>lifecycle-mapping</artifactId>
          <version>1.0.0</version>
          <configuration>
            <lifecycleMappingMetadata>
              <pluginExecutions>
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>com.mycila</groupId>
                    <artifactId>license-maven-plugin</artifactId>
                    <versionRange>[2.6,)</versionRange>
                    <goals>
                      <goal>format</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <ignore />
                  </action>
                </pluginExecution>
              </pluginExecutions>
            </lifecycleMappingMetadata>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <artifactId>maven-enforcer-plugin</artifactId>
        <executions>
          <execution>
            <id>enforce-dependency-rules</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <bannedDependencies>
                  <includes>
                    <include>net.bytebuddy:byte-buddy</include>
                  </includes>
                  <excludes>
                    <exclude>org.assertj:assertj-core</exclude>
                    <exclude>org.hamcrest:hamcrest-core</exclude>
                    <exclude>*:*:*:jar:compile</exclude>
                  </excludes>
                </bannedDependencies>
                <dependencyConvergence />
              </rules>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <source>1.8</source>
          <target>1.8</target>
          <encoding>${project.build.sourceEncoding}</encoding>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <argLine>${argLine}</argLine>
          <trimStackTrace>false</trimStackTrace>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-shade-plugin</artifactId>
        <version>3.2.3</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <createDependencyReducedPom>true</createDependencyReducedPom>
              <relocations>
                <relocation>
                  <pattern>net.bytebuddy</pattern>
                  <shadedPattern>org.assertj.core.internal.bytebuddy</shadedPattern>
                </relocation>
              </relocations>
              <filters>
                <filter>
                  <artifact>*:*</artifact>
                  <excludes>
                    <exclude>META-INF/versions/**</exclude>
                  </excludes>
                </filter>
              </filters>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-dependency-plugin</artifactId>
        <executions>
          <execution>
            <id>unpack-shade</id>
            <phase>package</phase>
            <goals>
              <goal>unpack</goal>
            </goals>
            <configuration>
              <artifactItems>
                <artifactItem>
                  <groupId>${project.groupId}</groupId>
                  <artifactId>${project.artifactId}</artifactId>
                  <version>${project.version}</version>
                </artifactItem>
              </artifactItems>
              <outputDirectory>${project.build.directory}/classes</outputDirectory>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>biz.aQute.bnd</groupId>
        <artifactId>bnd-maven-plugin</artifactId>
        <version>${bnd.version}</version>
        <executions>
          <execution>
            <id>bundle-after-unpack-shaded</id>
            <phase>package</phase>
            <goals>
              <goal>bnd-process</goal>
            </goals>
            <configuration>
              <bnd>Import-Package:\
                  !org.assertj.core.*,\
                  *;resolution:="optional"
                Multi-Release: true
                -exportcontents: \
                  !org.assertj.core.internal.*,\
                  org.assertj.core.*
                -noclassforname: true
                -removeheaders: Bnd-LastModified,Private-Package
                -fixupmessages: \
                  "Classes found in the wrong directory...";is:=ignore</bnd>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-antrun-plugin</artifactId>
        <version>3.0.0</version>
        <executions>
          <execution>
            <id>remove-primary-artifact</id>
            <phase>package</phase>
            <goals>
              <goal>run</goal>
            </goals>
            <configuration>
              <target>
                <script>project.getReference("maven.project").getArtifact().setFile(null);</script>
              </target>
            </configuration>
          </execution>
        </executions>
        <dependencies>
          <dependency>
            <groupId>org.beanshell</groupId>
            <artifactId>bsh</artifactId>
            <version>2.0b5</version>
          </dependency>
        </dependencies>
      </plugin>
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <executions>
          <execution>
            <id>default-jar</id>
            <phase>prepare-package</phase>
            <goals>
              <goal>jar</goal>
            </goals>
            <configuration>
              <archive>
                <manifestEntries>
                  <Multi-Release>true</Multi-Release>
                </manifestEntries>
              </archive>
            </configuration>
          </execution>
          <execution>
            <id>final-jar</id>
            <phase>package</phase>
            <goals>
              <goal>jar</goal>
            </goals>
            <configuration>
              <archive>
                <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
              </archive>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
        <executions>
          <execution>
            <id>jacoco-report</id>
            <phase>prepare-package</phase>
            <goals>
              <goal>report</goal>
            </goals>
          </execution>
          <execution>
            <id>default-check</id>
            <phase>prepare-package</phase>
            <goals>
              <goal>check</goal>
            </goals>
            <configuration>
              <rules>
                <rule>
                  <element>BUNDLE</element>
                  <limits>
                    <limit>
                      <counter>CLASS</counter>
                      <value>COVEREDRATIO</value>
                      <minimum>0.98</minimum>
                    </limit>
                  </limits>
                </rule>
              </rules>
            </configuration>
          </execution>
        </executions>
        <configuration>
          <excludes>
            <exclude>**/*hamcrest*/**</exclude>
          </excludes>
        </configuration>
      </plugin>
      <plugin>
        <groupId>net.alchim31.maven</groupId>
        <artifactId>yuicompressor-maven-plugin</artifactId>
        <executions>
          <execution>
            <phase>generate-sources</phase>
            <goals>
              <goal>compress</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <sourceDirectory>src/main/javadoc</sourceDirectory>
          <outputDirectory>${project.build.directory}/javadoc-stylesheet</outputDirectory>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-javadoc-plugin</artifactId>
        <configuration>
          <source>8</source>
          <stylesheetfile>${project.build.directory}/javadoc-stylesheet/assertj-javadoc-min.css</stylesheetfile>
          <additionalOptions>${javadocAdditionalOptions}</additionalOptions>
          <nohelp>true</nohelp>
          <show>private</show>
          <footer>&lt;link rel="stylesheet" href="./assertj-javadoc.css""&gt;
            &lt;script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/9.15.10/highlight.min.js"&gt;&lt;/script&gt;
            &lt;script type="text/javascript"&gt;hljs.initHighlightingOnLoad();&lt;/script&gt;</footer>
        </configuration>
      </plugin>
      <plugin>
        <groupId>biz.aQute.bnd</groupId>
        <artifactId>bnd-resolver-maven-plugin</artifactId>
        <version>${bnd.version}</version>
        <executions>
          <execution>
            <id>verify-osgi-metadata</id>
            <phase>pre-integration-test</phase>
            <goals>
              <goal>resolve</goal>
            </goals>
            <configuration>
              <bndruns>
                <bndrun>verify.bndrun</bndrun>
              </bndruns>
              <failOnChanges>false</failOnChanges>
              <reportOptional>false</reportOptional>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
  <profiles>
    <profile>
      <id>java9+</id>
      <build>
        <pluginManagement>
          <plugins>
            <plugin>
              <artifactId>maven-compiler-plugin</artifactId>
              <version>3.8.1</version>
              <executions>
                <execution>
                  <id>jdk9</id>
                  <goals>
                    <goal>compile</goal>
                  </goals>
                  <configuration>
                    <release>9</release>
                    <compileSourceRoots>
                      <compileSourceRoot>/home/<USER>/prog/assertj/assertj-core-3.x/target/checkout/src/main/java9</compileSourceRoot>
                    </compileSourceRoots>
                    <outputDirectory>/home/<USER>/prog/assertj/assertj-core-3.x/target/checkout/target/classes/META-INF/versions/9</outputDirectory>
                    <compilerArgs>
                      <arg>--patch-module</arg>
                      <arg>org.assertj.core=/home/<USER>/prog/assertj/assertj-core-3.x/target/checkout/target/classes</arg>
                      <arg>--module-version</arg>
                      <arg>3.16.1</arg>
                    </compilerArgs>
                  </configuration>
                </execution>
              </executions>
            </plugin>
          </plugins>
        </pluginManagement>
      </build>
    </profile>
    <profile>
      <id>java11+</id>
      <properties>
        <sonar.skip>false</sonar.skip>
      </properties>
    </profile>
    <profile>
      <id>java13+</id>
      <properties>
        <javadocAdditionalOptions>-html5 --allow-script-in-comments</javadocAdditionalOptions>
      </properties>
    </profile>
  </profiles>
  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.13</version>
      <scope>provided</scope>
      <exclusions>
        <exclusion>
          <artifactId>hamcrest-core</artifactId>
          <groupId>org.hamcrest</groupId>
        </exclusion>
      </exclusions>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-api</artifactId>
      <version>5.6.2</version>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter</artifactId>
      <version>5.6.2</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>junit-jupiter-params</artifactId>
          <groupId>org.junit.jupiter</groupId>
        </exclusion>
        <exclusion>
          <artifactId>junit-jupiter-engine</artifactId>
          <groupId>org.junit.jupiter</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.junit.vintage</groupId>
      <artifactId>junit-vintage-engine</artifactId>
      <version>5.6.2</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>junit-platform-engine</artifactId>
          <groupId>org.junit.platform</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.junit.platform</groupId>
      <artifactId>junit-platform-testkit</artifactId>
      <version>1.6.2</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>assertj-core</artifactId>
          <groupId>org.assertj</groupId>
        </exclusion>
        <exclusion>
          <artifactId>junit-platform-launcher</artifactId>
          <groupId>org.junit.platform</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.opentest4j</groupId>
      <artifactId>opentest4j</artifactId>
      <version>1.2.0</version>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.hamcrest</groupId>
      <artifactId>hamcrest</artifactId>
      <version>2.2</version>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <version>3.3.3</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>byte-buddy-agent</artifactId>
          <groupId>net.bytebuddy</groupId>
        </exclusion>
        <exclusion>
          <artifactId>objenesis</artifactId>
          <groupId>org.objenesis</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-junit-jupiter</artifactId>
      <version>3.3.3</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.github.marschall</groupId>
      <artifactId>memoryfilesystem</artifactId>
      <version>2.1.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
      <version>29.0-jre</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>failureaccess</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
        <exclusion>
          <artifactId>listenablefuture</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jsr305</artifactId>
          <groupId>com.google.code.findbugs</groupId>
        </exclusion>
        <exclusion>
          <artifactId>checker-qual</artifactId>
          <groupId>org.checkerframework</groupId>
        </exclusion>
        <exclusion>
          <artifactId>error_prone_annotations</artifactId>
          <groupId>com.google.errorprone</groupId>
        </exclusion>
        <exclusion>
          <artifactId>j2objc-annotations</artifactId>
          <groupId>com.google.j2objc</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <version>3.10</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
      <version>2.6</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>nl.jqno.equalsverifier</groupId>
      <artifactId>equalsverifier</artifactId>
      <version>3.1.13</version>
      <scope>test</scope>
    </dependency>
  </dependencies>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>net.bytebuddy</groupId>
        <artifactId>byte-buddy</artifactId>
        <version>${byte-buddy.version}</version>
      </dependency>
      <dependency>
        <groupId>net.bytebuddy</groupId>
        <artifactId>byte-buddy-agent</artifactId>
        <version>${byte-buddy.version}</version>
      </dependency>
      <dependency>
        <groupId>org.hamcrest</groupId>
        <artifactId>hamcrest</artifactId>
        <version>2.2</version>
      </dependency>
      <dependency>
        <groupId>org.opentest4j</groupId>
        <artifactId>opentest4j</artifactId>
        <version>1.2.0</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <properties>
    <byte-buddy.version>1.10.10</byte-buddy.version>
    <bnd.version>5.0.1</bnd.version>
    <junit-jupiter.version>5.6.2</junit-jupiter.version>
    <javadocAdditionalOptions>-html5 --allow-script-in-comments --no-module-directories</javadocAdditionalOptions>
    <junit.version>4.13</junit.version>
    <mockito.version>3.3.3</mockito.version>
    <jacoco-maven-plugin.version>0.8.5</jacoco-maven-plugin.version>
  </properties>
</project>
