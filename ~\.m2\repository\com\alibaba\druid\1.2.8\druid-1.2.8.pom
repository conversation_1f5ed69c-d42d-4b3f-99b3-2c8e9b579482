<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.alibaba</groupId>
	<artifactId>druid</artifactId>
	<version>1.2.8</version>
	<packaging>jar</packaging>
	<name>druid</name>
	<description>A JDBC datasource implementation.</description>
	<url>https://github.com/alibaba/druid</url>
	<inceptionYear>2013</inceptionYear>

	<properties>
		<spring.version>4.3.20.RELEASE</spring.version>
		<junit.version>4.13.1</junit.version>
		<jmh.version>1.19</jmh.version>

		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<jdk.version>1.8</jdk.version>
	</properties>

	<developers>
		<developer>
			<id>wenshao</id>
			<name>wenshao</name>
			<email><EMAIL></email>
		</developer>
		<developer>
			<id>kiki</id>
			<name>kiki</name>
			<email><EMAIL></email>
		</developer>
		<developer>
			<id>yako</id>
			<name>yako</name>
			<email><EMAIL></email>
		</developer>
	</developers>

	<scm>
		<url>https://<EMAIL>/alibaba/druid.git</url>
		<connection>scm:git:https://<EMAIL>/alibaba/druid.git</connection>
	</scm>

	<organization>
		<name>Alibaba Group</name>
		<url>http://code.alibabatech.com/</url>
	</organization>

	<licenses>
		<license>
			<name>Apache 2</name>
			<url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
			<distribution>repo</distribution>
			<comments>A business-friendly OSS license</comments>
		</license>
	</licenses>

	<issueManagement>
		<system>Jira</system>
		<url>http://code.alibabatech.com/jira/browse/DRUID</url>
	</issueManagement>
	<build>
		<extensions>
			<extension>
				<groupId>org.apache.maven.wagon</groupId>
				<artifactId>wagon-webdav</artifactId>
				<version>1.0-beta-2</version>
			</extension>
		</extensions>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.8.0</version>
				<configuration>
					<encoding>UTF-8</encoding>
					<source>${jdk.version}</source>
					<target>${jdk.version}</target>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>2.22.1</version>
				<configuration>
					<includes>
						<include>**/bvt/**/*.java</include>
					</includes>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-source-plugin</artifactId>
				<version>3.0.1</version>
				<executions>
					<execution>
						<id>attach-sources</id>
						<goals>
							<goal>jar-no-fork</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<attach>true</attach>
				</configuration>
			</plugin>

			<plugin>
				<artifactId>maven-javadoc-plugin</artifactId>
				<version>3.0.1</version>
				<executions>
					<execution>
						<id>attach-javadoc</id>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<show>public</show>
					<charset>UTF-8</charset>
					<encoding>UTF-8</encoding>
					<docencoding>UTF-8</docencoding>
					<links>
						<link>http://docs.oracle.com/javase/6/docs/api</link>
					</links>
				</configuration>
			</plugin>

			<plugin>
				<artifactId>maven-gpg-plugin</artifactId>
				<version>1.6</version>
				<executions>
					<execution>
						<id>sign-artifacts</id>
						<phase>verify</phase>
						<goals>
							<goal>sign</goal>
						</goals>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<version>3.1.1</version>
				<configuration>
					<archive>
						<manifest>
							<addDefaultImplementationEntries>true</addDefaultImplementationEntries>
						</manifest>
						<manifestEntries>
							<Implementation-Build>${buildNumber}</Implementation-Build>
						</manifestEntries>
					</archive>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>buildnumber-maven-plugin</artifactId>
				<version>1.1</version>
				<executions>
					<execution>
						<phase>validate</phase>
						<goals>
							<goal>create</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<format>{0,date,yyyy-MM-dd HH:mm:ss}</format>
					<items>
						<item>timestamp</item>
					</items>
					<doCheck>false</doCheck>
					<doUpdate>true</doUpdate>
				</configuration>
			</plugin>
		</plugins>
	</build>

	<profiles>
		<profile>
			<id>jconsole</id>
			<activation>
				<!--<activeByDefault>true</activeByDefault>-->
				<file>
					<exists>${env.JAVA_HOME}/lib/jconsole.jar</exists>
				</file>
			</activation>
			<properties>
				<toolsjar>${env.JAVA_HOME}/lib/tools.jar</toolsjar>
				<jconsolejar>${env.JAVA_HOME}/lib/jconsole.jar</jconsolejar>
			</properties>

			<dependencies>
				<dependency>
					<groupId>com.alibaba</groupId>
					<artifactId>jconsole</artifactId>
					<version>1.6.0</version>
					<scope>system</scope>
					<systemPath>${jconsolejar}</systemPath>
					<optional>true</optional>
				</dependency>
				<dependency>
					<groupId>com.alibaba</groupId>
					<artifactId>tools</artifactId>
					<version>1.6.0</version>
					<scope>system</scope>
					<systemPath>${toolsjar}</systemPath>
					<optional>true</optional>
				</dependency>
			</dependencies>
		</profile>
		<profile>
			<id>java11plus</id>
			<activation>
				<jdk>[11,)</jdk>
			</activation>
			<dependencies>
				<dependency>
					<groupId>javax.annotation</groupId>
					<artifactId>javax.annotation-api</artifactId>
					<version>1.3.2</version>
				</dependency>
			</dependencies>
		</profile>
		<profile>
			<id>mac-profile</id>
			<activation>
				<activeByDefault>false</activeByDefault>
				<file>
					<exists>${java.home}/../Classes/jconsole.jar</exists>
				</file>
			</activation>
			<dependencies>
				<dependency>
					<groupId>com.alibaba</groupId>
					<artifactId>jconsole</artifactId>
					<version>1.6.0</version>
					<scope>system</scope>
					<systemPath>${java.home}/../Classes/jconsole.jar</systemPath>
					<optional>true</optional>
				</dependency>
			</dependencies>
		</profile>

		<profile>
			<id>mac-profile-oracle-jdk</id>
			<activation>
				<activeByDefault>false</activeByDefault>
				<file>
					<exists>${java.home}/../lib/jconsole.jar</exists>
				</file>
			</activation>
			<dependencies>
				<dependency>
					<groupId>com.alibaba</groupId>
					<artifactId>jconsole</artifactId>
					<version>1.8.0</version>
					<scope>system</scope>
					<systemPath>${java.home}/../lib/jconsole.jar</systemPath>
					<optional>true</optional>
				</dependency>

				<dependency>
					<groupId>com.alibaba</groupId>
					<artifactId>tools</artifactId>
					<version>1.8.0</version>
					<scope>system</scope>
					<systemPath>${java.home}/../lib/tools.jar</systemPath>
					<optional>true</optional>
				</dependency>
			</dependencies>
		</profile>
		<profile>
			<id>travis</id>
			<activation>
				<property>
					<name>env.TRAVIS</name>
					<value>true</value>
				</property>
			</activation>
			<build>
				<plugins>
					<plugin>
						<!-- for codecov.io -->
						<!-- config example: https://github.com/codecov/example-java -->
						<!-- plugin docs: http://eclemma.org/jacoco/trunk/doc/ -->
						<groupId>org.jacoco</groupId>
						<artifactId>jacoco-maven-plugin</artifactId>
						<version>0.7.6.201602180812</version>
						<executions>
							<execution>
								<goals>
									<goal>prepare-agent</goal>
								</goals>
							</execution>
							<execution>
								<id>report</id>
								<phase>test</phase>
								<goals>
									<goal>report</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
	</profiles>

	<dependencies>
		<dependency>
			<groupId>simple-jndi</groupId>
			<artifactId>simple-jndi</artifactId>
			<version>0.11.4.1</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>javax.transaction</groupId>
			<artifactId>jta</artifactId>
			<version>1.1</version>
			<scope>provided</scope>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
			<version>3.1.0</version>
			<scope>provided</scope>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>commons-logging</groupId>
			<artifactId>commons-logging</artifactId>
			<version>1.2</version>
			<scope>provided</scope>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-core</artifactId>
			<version>${spring.version}</version>
			<scope>provided</scope>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-beans</artifactId>
			<version>${spring.version}</version>
			<scope>provided</scope>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-orm</artifactId>
			<version>${spring.version}</version>
			<scope>provided</scope>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-webmvc</artifactId>
			<version>${spring.version}</version>
			<scope>provided</scope>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-ibatis</artifactId>
			<version>2.0.8</version>
			<scope>provided</scope>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.mybatis</groupId>
			<artifactId>mybatis</artifactId>
			<version>3.4.0</version>
			<scope>provided</scope>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.mybatis</groupId>
			<artifactId>mybatis-spring</artifactId>
			<version>1.3.0</version>
			<scope>provided</scope>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>log4j</groupId>
			<artifactId>log4j</artifactId>
			<version>1.2.17</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
			<version>1.7.9</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-log4j12</artifactId>
			<version>1.7.9</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-api</artifactId>
			<version>2.13.2</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-core</artifactId>
			<version>2.13.2</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<version>8.0.21</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>net.sourceforge.jtds</groupId>
			<artifactId>jtds</artifactId>
			<version>1.3.0</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.postgresql</groupId>
			<artifactId>postgresql</artifactId>
			<version>42.2.18</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>com.oracle.ojdbc</groupId>
			<artifactId>ojdbc8</artifactId>
			<version>19.3.0.0</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.kylin</groupId>
			<artifactId>kylin-jdbc</artifactId>
			<version>2.0.0</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>io.trino</groupId>
			<artifactId>trino-jdbc</artifactId>
			<version>352</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.quartz-scheduler</groupId>
			<artifactId>quartz</artifactId>
			<version>2.3.2</version>
			<scope>provided</scope>
			<optional>true</optional>
		</dependency>


		<dependency>
			<groupId>org.apache.ibatis</groupId>
			<artifactId>ibatis-sqlmap</artifactId>
			<version>2.3.4.726</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>com.h2database</groupId>
			<artifactId>h2</artifactId>
			<version>1.4.199</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-core</artifactId>
			<version>5.1.0.Final</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-c3p0</artifactId>
			<version>5.1.0.Final</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.aliyun.odps</groupId>
			<artifactId>odps-sdk-udf</artifactId>
			<version>0.37.10-public</version>
			<scope>provided</scope>
			<optional>true</optional>
			<exclusions>
				<exclusion>
					<groupId>javax.validation</groupId>
					<artifactId>validation-api</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.calcite</groupId>
			<artifactId>calcite-core</artifactId>
			<version>1.26.0</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>30.1-jre</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>dubbo</artifactId>
			<version>2.5.3</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.curator</groupId>
			<artifactId>curator-recipes</artifactId>
			<version>4.2.0</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<version>1.2.73</version>
			<scope>provided</scope>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.apache.hive</groupId>
			<artifactId>hive-jdbc</artifactId>
			<version>3.1.2</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>ru.yandex.clickhouse</groupId>
			<artifactId>clickhouse-jdbc</artifactId>
			<version>0.2.4</version>
			<scope>provided</scope>
		</dependency>

    </dependencies>
</project>
