<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.apiguardian</groupId>
  <artifactId>apiguardian-api</artifactId>
  <version>1.1.0</version>
  <name>org.apiguardian:apiguardian-api</name>
  <description>@API Guardian</description>
  <url>https://github.com/apiguardian-team/apiguardian</url>
  <licenses>
    <license>
      <name>The Apache License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>apiguardian</id>
      <name>@API Guardian Team</name>
      <email><EMAIL></email>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:git://github.com/apiguardian-team/apiguardian.git</connection>
    <developerConnection>scm:git:git://github.com/apiguardian-team/apiguardian.git</developerConnection>
    <url>https://github.com/apiguardian-team/apiguardian</url>
  </scm>
</project>
