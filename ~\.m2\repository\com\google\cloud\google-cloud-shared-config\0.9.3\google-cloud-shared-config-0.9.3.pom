<?xml version='1.0' encoding='UTF-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.cloud</groupId>
  <artifactId>google-cloud-shared-config</artifactId>
  <packaging>pom</packaging>
  <version>0.9.3</version><!-- {x-version-update:google-cloud-shared-config:current} -->
  <name>Google Cloud</name>
  <url>https://github.com/googleapis/java-shared-config</url>
  <description>
    Shared build configuration for Google Cloud Java libraries.
  </description>
  <developers>
    <developer>
      <id>chingor13</id>
      <name><PERSON></name>
      <email><EMAIL></email>
      <organization>Google</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
  </developers>
  <organization>
    <name>Google LLC</name>
  </organization>
  <scm>
    <connection>scm:git:**************:googleapis/java-shared-config.git</connection>
    <developerConnection>scm:git:**************:googleapis/java-shared-config.git</developerConnection>
    <url>https://github.com/googleapis/java-shared-config</url>
    <tag>HEAD</tag>
  </scm>

  <issueManagement>
    <url>https://github.com/googleapis/java-shared-config/issues</url>
    <system>GitHub Issues</system>
  </issueManagement>

  <distributionManagement>
    <snapshotRepository>
      <id>sonatype-nexus-snapshots</id>
      <url>https://oss.sonatype.org/content/repositories/snapshots</url>
    </snapshotRepository>
    <repository>
      <id>sonatype-nexus-staging</id>
      <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
  </distributionManagement>

  <licenses>
    <license>
      <name>Apache-2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
    </license>
  </licenses>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <!-- Template values used in site generation -->
    <site.installationModule>${project.artifactId}</site.installationModule>
    <report.jxr.inherited>false</report.jxr.inherited>
    <skipITs>true</skipITs>
    <auto-value-annotation.version>1.7.4</auto-value-annotation.version>
    <docRoot>/java/docs/reference/</docRoot>
  </properties>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.sonatype.plugins</groupId>
          <artifactId>nexus-staging-maven-plugin</artifactId>
          <version>1.6.8</version>
          <extensions>true</extensions>
          <configuration>
            <serverId>ossrh</serverId>
            <nexusUrl>https://oss.sonatype.org/</nexusUrl>
            <autoReleaseAfterClose>false</autoReleaseAfterClose>
            <stagingProgressTimeoutMinutes>15</stagingProgressTimeoutMinutes>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>3.0.0-M5</version>
          <configuration>
            <!-- Excludes integration tests and smoke tests when unit tests are run -->
            <excludes>
              <exclude>**/*SmokeTest.java</exclude>
              <exclude>**/IT*.java</exclude>
            </excludes>
            <reportNameSuffix>sponge_log</reportNameSuffix>
          </configuration>
          <dependencies>
            <dependency>
              <groupId>org.apache.maven.surefire</groupId>
              <artifactId>surefire-junit47</artifactId>
              <version>3.0.0-M5</version>
            </dependency>
          </dependencies>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-enforcer-plugin</artifactId>
          <version>3.0.0-M3</version>
          <dependencies>
            <dependency>
              <groupId>org.codehaus.mojo</groupId>
              <artifactId>extra-enforcer-rules</artifactId>
              <version>1.3</version>
            </dependency>
          </dependencies>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>exec-maven-plugin</artifactId>
          <version>1.6.0</version>
          <configuration>
            <!-- to get rid of the warning: [WARNING] Warning: killAfter is now deprecated. Do you need it ?
                 Please comment on MEXEC-6. see: method execute() in
                 https://github.com/ispringer/exec-maven-plugin/blob/master/src/main/java/org/codehaus/mojo/exec/ExecJavaMojo.java
            -->
            <killAfter>-1</killAfter>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-failsafe-plugin</artifactId>
          <version>3.0.0-M4</version>
          <configuration>
            <forkedProcessTimeoutInSeconds>1200</forkedProcessTimeoutInSeconds>
            <reportNameSuffix>sponge_log</reportNameSuffix>
            <includes>
              <include>**/IT*.java</include>
              <include>**/*SmokeTest.java</include>
            </includes>
          </configuration>
          <dependencies>
            <dependency>
              <groupId>org.apache.maven.surefire</groupId>
              <artifactId>surefire-junit47</artifactId>
              <version>3.0.0-M4</version>
            </dependency>
          </dependencies>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jar-plugin</artifactId>
          <version>3.2.0</version>
          <configuration>
            <archive>
              <addMavenDescriptor>true</addMavenDescriptor>
              <index>true</index>
              <manifest>
                <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
              </manifest>
              <manifestEntries>
                <artifactId>${project.artifactId}</artifactId>
                <groupId>${project.groupId}</groupId>
                <version>${project.version}</version>
              </manifestEntries>
            </archive>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>3.8.1</version>
          <configuration>
            <source>1.7</source>
            <target>1.7</target>
            <encoding>UTF-8</encoding>
            <compilerArgument>-Xlint:unchecked</compilerArgument>
            <compilerArgument>-Xlint:deprecation</compilerArgument>
            <showDeprecation>true</showDeprecation>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-site-plugin</artifactId>
          <version>3.9.1</version>
          <configuration>
            <skipDeploy>true</skipDeploy>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.jacoco</groupId>
          <artifactId>jacoco-maven-plugin</artifactId>
          <version>0.8.6</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-dependency-plugin</artifactId>
          <version>3.1.2</version>
          <configuration>
            <ignoredUnusedDeclaredDependencies>
              <ignoredUnusedDeclaredDependency>javax.annotation:javax.annotation-api</ignoredUnusedDeclaredDependency>
            </ignoredUnusedDeclaredDependencies>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>build-helper-maven-plugin</artifactId>
          <version>3.2.0</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>clirr-maven-plugin</artifactId>
          <version>2.8</version>
        </plugin>
        <plugin>
          <groupId>com.coveo</groupId>
          <artifactId>fmt-maven-plugin</artifactId>
          <version>2.9</version>
          <configuration>
            <style>google</style>
            <verbose>true</verbose>
          </configuration>
          <dependencies>
            <dependency>
              <groupId>com.google.googlejavaformat</groupId>
              <artifactId>google-java-format</artifactId>
              <version>1.7</version>
            </dependency>
          </dependencies>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>flatten-maven-plugin</artifactId>
          <version>1.2.5</version>
          <executions>
            <!-- enable flattening -->
            <execution>
              <id>flatten</id>
              <phase>process-resources</phase>
              <goals>
                <goal>flatten</goal>
              </goals>
            </execution>
            <!-- ensure proper cleanup -->
            <execution>
              <id>flatten.clean</id>
              <phase>clean</phase>
              <goals>
                <goal>clean</goal>
              </goals>
            </execution>
          </executions>
          <configuration>
            <flattenMode>oss</flattenMode>
            <flattenDependencyMode>all</flattenDependencyMode>
            <pomElements>
              <build>remove</build>
            </pomElements>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
        <executions>
          <execution>
            <id>enforce</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <requireMavenVersion>
                  <version>[3.0,)</version>
                </requireMavenVersion>
                <requireJavaVersion>
                  <version>[1.7,)</version>
                </requireJavaVersion>
                <requireUpperBoundDeps/>
                <banDuplicateClasses>
                  <scopes>
                    <scope>compile</scope>
                    <scope>provided</scope>
                  </scopes>
                  <findAllDuplicates>true</findAllDuplicates>
                  <ignoreWhenIdentical>true</ignoreWhenIdentical>
                </banDuplicateClasses>
              </rules>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>exec-maven-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>java</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-failsafe-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>integration-test</goal>
              <goal>verify</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>test-jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.sonatype.plugins</groupId>
        <artifactId>nexus-staging-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-site-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>prepare-agent</goal>
            </goals>
          </execution>
          <execution>
            <id>report</id>
            <phase>test</phase>
            <goals>
              <goal>report</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>build-helper-maven-plugin</artifactId>
        <executions>
          <execution>
            <id>add-main-proto-resources</id>
            <phase>generate-resources</phase>
            <goals>
              <goal>add-resource</goal>
            </goals>
            <configuration>
              <resources>
                <resource>
                  <directory>src/main/proto</directory>
                </resource>
              </resources>
            </configuration>
          </execution>
          <execution>
            <id>add-test-proto-resources</id>
            <phase>generate-test-resources</phase>
            <goals>
              <goal>add-test-resource</goal>
            </goals>
            <configuration>
              <resources>
                <resource>
                  <directory>src/test/proto</directory>
                </resource>
              </resources>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>clirr-maven-plugin</artifactId>
        <configuration>
          <ignoredDifferencesFile>clirr-ignored-differences.xml</ignoredDifferencesFile>
          <logResults>true</logResults>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

  <reporting>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-project-info-reports-plugin</artifactId>
        <version>3.1.1</version>
        <reportSets>
          <reportSet>
            <reports>
              <report>index</report>
              <report>dependency-info</report>
              <report>team</report>
              <report>ci-management</report>
              <report>issue-management</report>
              <report>licenses</report>
              <report>scm</report>
              <report>dependency-management</report>
              <report>distribution-management</report>
              <report>summary</report>
              <report>modules</report>
            </reports>
          </reportSet>
        </reportSets>
        <configuration>
          <dependencyDetailsEnabled>true</dependencyDetailsEnabled>
          <artifactId>${site.installationModule}</artifactId>
          <packaging>jar</packaging>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>3.2.0</version>
        <reportSets>
          <reportSet>
            <id>html</id>
            <reports>
              <report>javadoc</report>
              <report>aggregate</report>
              <report>aggregate-jar</report>
            </reports>
          </reportSet>
        </reportSets>
        <configuration>
          <doclint>none</doclint>
          <show>protected</show>
          <nohelp>true</nohelp>
          <outputDirectory>${project.build.directory}/javadoc</outputDirectory>
          <groups>
            <group>
              <title>Test helpers packages</title>
              <packages>com.google.cloud.testing</packages>
            </group>
            <group>
              <title>SPI packages</title>
              <packages>com.google.cloud.spi*</packages>
            </group>
          </groups>

          <links>
            <link>https://googleapis.dev/java/api-common/</link>
            <link>https://googleapis.dev/java/gax/</link>
            <link>https://googleapis.dev/java/google-auth-library/</link>

            <link>https://developers.google.com/protocol-buffers/docs/reference/java/</link>
            <link>https://googleapis.github.io/common-protos-java/apidocs/</link>
            <link>https://grpc.io/grpc-java/javadoc/</link>
          </links>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jxr-plugin</artifactId>
        <version>3.0.0</version>
        <configuration>
          <linkJavadoc>true</linkJavadoc>
        </configuration>
        <reportSets>
          <reportSet>
            <id>aggregate</id>
            <inherited>${report.jxr.inherited}</inherited>
            <reports>
              <report>jxr</report>
              <report>aggregate</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
    </plugins>
  </reporting>

  <dependencyManagement>
    <dependencies>
      <!--
      We would prefer this be defined in our shared-dependencies, however due to
      auto value being part of the compiler annotation processor, we are defining
      it here to reduce the locations for version management.
      -->
      <dependency>
        <groupId>com.google.auto.value</groupId>
        <artifactId>auto-value-annotations</artifactId>
        <version>${auto-value-annotation.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <profiles>
    <profile>
      <id>release</id>
      <activation>
        <property>
          <name>performRelease</name>
        </property>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-source-plugin</artifactId>
            <version>3.2.1</version>
            <executions>
              <execution>
                <id>attach-sources</id>
                <goals>
                  <goal>jar-no-fork</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <version>3.2.0</version>
            <executions>
              <execution>
                <id>attach-javadocs</id>
                <goals>
                  <goal>jar</goal>
                </goals>
                <configuration>
                  <doclint>none</doclint>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
            <version>1.6</version>
            <executions>
              <execution>
                <id>sign-artifacts</id>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
                <configuration>
                  <gpgArguments>
                    <arg>--pinentry-mode</arg>
                    <arg>loopback</arg>
                  </gpgArguments>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>allow-snapshots</id>
      <repositories>
        <repository>
          <id>sonatype-snapshots</id>
          <url>https://oss.sonatype.org/content/repositories/snapshots</url>
          <releases>
            <enabled>false</enabled>
          </releases>
          <snapshots>
            <enabled>true</enabled>
          </snapshots>
        </repository>
      </repositories>
    </profile>
    <profile>
      <!-- Only run checkstyle plugin on Java 8+ (checkstyle artifact only supports Java 8+) -->
      <id>checkstyle-tests</id>
      <activation>
        <jdk>[1.8,)</jdk>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-checkstyle-plugin</artifactId>
            <version>3.1.1</version>
            <dependencies>
              <dependency>
                <groupId>com.puppycrawl.tools</groupId>
                <artifactId>checkstyle</artifactId>
                <version>8.29</version>
              </dependency>
            </dependencies>
            <executions>
              <execution>
                <id>checkstyle</id>
                <phase>validate</phase>
                <goals>
                  <goal>check</goal>
                </goals>
                <configuration>
                  <headerLocation>java.header</headerLocation>
                  <configLocation>license-checks.xml</configLocation>
                  <consoleOutput>true</consoleOutput>
                  <failOnViolation>true</failOnViolation>
                  <violationSeverity>error</violationSeverity>
                  <failsOnError>true</failsOnError>
                  <includeTestSourceDirectory>true</includeTestSourceDirectory>
                  <!-- Explicitly set the source directory to avoid running checkstyle on generated sources. -->
                  <sourceDirectories>
                    <sourceDirectory>src/main</sourceDirectory>
                  </sourceDirectories>
                  <testSourceDirectories>
                    <testSourceDirectory>src/test</testSourceDirectory>
                  </testSourceDirectories>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>enable-integration-tests</id>
      <properties>
        <skipITs>false</skipITs>
      </properties>
    </profile>
    <profile>
      <id>devsite-apidocs</id>
      <activation>
        <property>
          <!-- Activate with the -Ddevsite.template=/path/to/templates flag. -->
          <name>devsite.template</name>
        </property>
      </activation>
      <build>
        <plugins>
          <plugin>
            <!-- Generate API docs using Doclava for the developer site. -->
            <artifactId>maven-javadoc-plugin</artifactId>
            <!-- Note: version 3.x.x uses additionalOption instead of additionalparam. -->
            <version>3.2.0</version>
            <executions>
              <execution>
                <phase>site</phase>
                <goals>
                  <goal>aggregate</goal>
                </goals>
              </execution>
            </executions>
            <configuration>
              <docletArtifact>
                <groupId>com.google.doclava</groupId>
                <artifactId>doclava</artifactId>
                <version>1.0.6</version>
              </docletArtifact>
              <doclet>com.google.doclava.Doclava</doclet>
              <bootclasspath>${sun.boot.class.path}</bootclasspath>
              <additionalDependencies>
                <additionalDependency>
                  <groupId>com.google.j2objc</groupId>
                  <artifactId>j2objc-annotations</artifactId>
                  <version>1.3</version>
                </additionalDependency>
              </additionalDependencies>
              <additionalOptions>
                <additionalOption>-hdf</additionalOption>
                <additionalOption>book.path</additionalOption>
                <additionalOption>/java/_book.yaml</additionalOption>
                <additionalOption>-hdf</additionalOption>
                <additionalOption>project.path</additionalOption>
                <additionalOption>/java/_project.yaml</additionalOption>
                <additionalOption>-hdf</additionalOption>
                <additionalOption>devsite.path</additionalOption>
                <additionalOption>${docRoot}</additionalOption>
                <additionalOption>-d</additionalOption>
                <additionalOption>${project.build.directory}/devsite</additionalOption>
                <additionalOption>-templatedir</additionalOption>
                <additionalOption>${devsite.template}</additionalOption>
                <additionalOption>-toroot</additionalOption>
                <additionalOption>${docRoot}</additionalOption>
                <additionalOption>-yaml</additionalOption>
                <additionalOption>_toc.yaml</additionalOption>
                <additionalOption>-warning</additionalOption>
                <additionalOption>101</additionalOption>
              </additionalOptions>
              <useStandardDocletOptions>false</useStandardDocletOptions>
            </configuration>
          </plugin>
          <plugin>
            <!-- Clean up some references and files. -->
            <artifactId>maven-antrun-plugin</artifactId>
            <version>1.8</version>
            <executions>
              <execution>
                <phase>site</phase>
                <goals>
                  <goal>run</goal>
                </goals>
                <configuration>
                  <target>
                    <echo message="Updating relative links in API docs" />
                    <!-- TODO: What is the right behavior for io* and google*? -->
                    <replace dir="${project.build.directory}/devsite" token="href=&quot;com" value="href=&quot;${docRoot}com" />
                    <replace dir="${project.build.directory}/devsite" token="href=&quot;io" value="href=&quot;${docRoot}io" />
                    <replace dir="${project.build.directory}/devsite" token="href=&quot;google" value="href=&quot;${docRoot}google" />
                    <copy file="${project.build.directory}/devsite/assets/_toc.yaml" todir="${project.build.directory}/devsite/reference" />
                    <echo message="Removing files not needed by Devsite" />
                    <delete file="${project.build.directory}/devsite/reference/classes.html" />
                    <delete file="${project.build.directory}/devsite/reference/hierarchy.html" />
                    <delete file="${project.build.directory}/devsite/reference/index.html" />
                    <delete file="${project.build.directory}/devsite/reference/lists.js" />
                    <delete file="${project.build.directory}/devsite/reference/packages.html" />
                    <delete file="${project.build.directory}/devsite/reference/current.xml" />
                  </target>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>autovalue-java7</id>
      <activation>
        <jdk>1.7</jdk>
        <file>
          <exists>${basedir}/EnableAutoValue.txt</exists>
        </file>
      </activation>
      <properties>
        <!--
        We need to back pin to 1.4 because it is the last version of auto-value
        that was compiled for java 1.7.
        -->
        <auto-value.version>1.4</auto-value.version>
      </properties>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-compiler-plugin</artifactId>
            <configuration>
              <annotationProcessorPaths>
                <path>
                  <groupId>com.google.auto.value</groupId>
                  <artifactId>auto-value</artifactId>
                  <version>${auto-value.version}</version>
                </path>
                <!--
                There is currently no available version of auto-service-annotations
                in maven central compiled for java 1.7, so we can't include it here.

                If you're using IntelliJ please use a newer jdk and set the language
                level to 1.7 for your dev work.
                -->
              </annotationProcessorPaths>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>autovalue-java8</id>
      <activation>
        <jdk>[1.8,)</jdk>
        <file>
          <exists>${basedir}/EnableAutoValue.txt</exists>
        </file>
      </activation>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-compiler-plugin</artifactId>
            <configuration>
              <annotationProcessorPaths>
                <path>
                  <groupId>com.google.auto.value</groupId>
                  <artifactId>auto-value</artifactId>
                  <version>${auto-value-annotation.version}</version>
                </path>
                <!--
                Manually pull in auto-service-annotations so that it is part of the
                processor path because auto-value has it set to provided scope.

                This dependency is needed due to the retention change in
                https://github.com/google/auto/commit/628df548685b4fc0f2a9af856f97cc2a68da246b
                where the RetentionPolicy changed from SOURCE to CLASS.

                Due to the RetentionPolicy change to CLASS we must have the
                annotations available on the processor path otherwise the following
                error will be thrown. (This is a particular problem with the
                annotation processor configuration in IntelliJ)

                Error:java: java.lang.NoClassDefFoundError: com/google/auto/service/AutoService
                  com.google.auto.service.AutoService
                -->
                <path>
                  <groupId>com.google.auto.service</groupId>
                  <artifactId>auto-service-annotations</artifactId>
                  <version>1.0-rc7</version>
                </path>
              </annotationProcessorPaths>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>

  </profiles>
</project>
