<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">  
  <modelVersion>4.0.0</modelVersion>  
  <groupId>com.howbuy.common</groupId>  
  <artifactId>common-service</artifactId>  
  <version>3.5.7-RELEASE</version>
  <packaging>jar</packaging>  
  <name>common-service</name>  
  <properties> 
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>  
    <maven.compiler.encoding>UTF-8</maven.compiler.encoding>  
    <java.version>1.6</java.version>  
    <activemq.version>5.10.2</activemq.version>  
    <spring.version>3.2.12.RELEASE</spring.version>
    <com.howbuy.common-facade.version>3.5.7-RELEASE</com.howbuy.common-facade.version>
    <com.howbuy.common-config.version>3.5.7-RELEASE</com.howbuy.common-config.version>
  </properties>
  <build> 
    <plugins> 
      <plugin> 
        <groupId>org.apache.maven.plugins</groupId>  
        <artifactId>maven-compiler-plugin</artifactId>  
        <version>2.3.2</version>  
        <configuration> 
          <source>${java.version}</source>  
          <target>${java.version}</target>  
          <encoding>UTF-8</encoding> 
        </configuration> 
      </plugin>  
      <!-- Java源码插件 -->  
      <plugin> 
        <groupId>org.apache.maven.plugins</groupId>  
        <artifactId>maven-source-plugin</artifactId>  
        <version>2.4</version>  
        <configuration> 
          <encoding>${project.build.sourceEncoding}</encoding> 
        </configuration>  
        <executions> 
          <execution> 
            <id>attach-source</id>  
            <phase>install</phase>  
            <goals> 
              <goal>jar-no-fork</goal> 
            </goals> 
          </execution> 
        </executions> 
      </plugin>  
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>  
        <artifactId>maven-surefire-plugin</artifactId>  
        <version>2.5</version>  
        <configuration> 
          <skipTests>true</skipTests> 
        </configuration> 
      </plugin> 
    </plugins> 
  </build>
    <distributionManagement>
      <repository>
        <id>releases</id>
        <name>nexus releases respository</name>
        <url>http://mvn.intelnal.howbuy.com/nexus/content/repositories/releases/</url>
      </repository>
      <snapshotRepository>
        <id>snapshots</id>
        <name>nexus snapshots respository</name>
        <url>http://mvn.intelnal.howbuy.com/nexus/content/repositories/snapshots/</url>
      </snapshotRepository>
    </distributionManagement>
  <dependencies> 
    <dependency> 
      <groupId>junit</groupId>  
      <artifactId>junit</artifactId>  
      <version>4.12</version>  
      <scope>test</scope> 
    </dependency>  
    <dependency> 
      <groupId>org.slf4j</groupId>  
      <artifactId>slf4j-api</artifactId>  
      <version>1.6.1</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
		<groupId>com.howbuy.pa.cache</groupId>
		<artifactId>howbuy-cache-client-trade</artifactId>
		<version>3.0-RELEASE</version>
		<scope>provided</scope>
		<exclusions>
			<exclusion>
				<artifactId>slf4j-api</artifactId>
				<groupId>org.slf4j</groupId>
			</exclusion>
		</exclusions>
	</dependency>
    <dependency>
      <groupId>org.apache.activemq</groupId>
      <artifactId>activemq-client</artifactId>  
      <version>${activemq.version}</version>  
      <scope>provided</scope> 
    </dependency>  
    <dependency> 
      <groupId>org.springframework</groupId>  
      <artifactId>spring-jms</artifactId>  
      <version>${spring.version}</version>  
      <scope>provided</scope> 
    </dependency>
    <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aspects</artifactId>
            <version>${spring.version}</version>
    </dependency>  
    <dependency> 
      <groupId>com.trilead</groupId>  
      <artifactId>trilead-ssh2</artifactId>  
      <version>1.0.0-build217</version> 
    </dependency> 
    <dependency>
    	<groupId>com.howbuy.common</groupId>
    	<artifactId>common-facade</artifactId>
    	<version>${com.howbuy.common-facade.version}</version>
    </dependency>
    <dependency>
      <groupId>com.howbuy.common</groupId>
      <artifactId>common-config</artifactId>
      <version>${com.howbuy.common-config.version}</version>
    </dependency>
    <!-- 缓存3.0升级 -->
    <dependency>
      <groupId>com.howbuy.pa.cache</groupId>
      <artifactId>howbuy-cache-client-trade</artifactId>
      <version>3.3.0-RELEASE</version>
	  <scope>provided</scope>
    </dependency>
  </dependencies> 
</project>