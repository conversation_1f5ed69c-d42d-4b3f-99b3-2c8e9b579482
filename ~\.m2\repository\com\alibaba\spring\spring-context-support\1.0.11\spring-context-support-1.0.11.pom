<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.sonatype.oss</groupId>
        <artifactId>oss-parent</artifactId>
        <version>7</version>
    </parent>

    <groupId>com.alibaba.spring</groupId>
    <artifactId>spring-context-support</artifactId>
    <version>1.0.11</version>

    <properties>
        <maven.compiler.source>1.6</maven.compiler.source>
        <maven.compiler.target>1.6</maven.compiler.target>
        <spring.framework.version>3.2.18.RELEASE</spring.framework.version>
    </properties>

    <scm>
        <connection>**************:alibaba/spring-context-support.git</connection>
        <developerConnection>**************:alibaba/spring-context-support.git</developerConnection>
        <url>https://github.com/alibaba/spring-context-support</url>
    </scm>

    <developers>

        <developer>
            <id>mercyblitz</id>
            <name>Mercy Ma</name>
            <email><EMAIL></email>
            <url>https://github.com/mercyblitz/</url>
            <roles>
                <role>Developer</role>
            </roles>
        </developer>

    </developers>

    <dependencies>

        <!-- Spring Framework -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
            <version>${spring.framework.version}</version>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.13.1</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <version>${spring.framework.version}</version>
            <scope>test</scope>
        </dependency>

    </dependencies>

    <profiles>
        <profile>
            <id>release</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                        <version>3.1.1</version>
                        <executions>
                            <execution>
                                <phase>package</phase>
                                <goals>
                                    <goal>jar</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <version>1.6</version>
                        <executions>
                            <execution>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>spring-4.0</id>
            <properties>
                <spring.framework.version>4.0.9.RELEASE</spring.framework.version>
            </properties>
        </profile>

        <profile>
            <id>spring-4.1</id>
            <properties>
                <spring.framework.version>4.1.9.RELEASE</spring.framework.version>
            </properties>
        </profile>

        <profile>
            <id>spring-4.2</id>
            <properties>
                <spring.framework.version>4.2.9.RELEASE</spring.framework.version>
            </properties>
        </profile>

        <profile>
            <id>spring-4.3</id>
            <properties>
                <spring.framework.version>4.3.29.RELEASE</spring.framework.version>
            </properties>
        </profile>

        <profile>
            <id>spring-5.0</id>
            <properties>
                <spring.framework.version>5.0.19.RELEASE</spring.framework.version>
            </properties>
        </profile>

        <profile>
            <id>spring-5.1</id>
            <properties>
                <spring.framework.version>5.1.18.RELEASE</spring.framework.version>
            </properties>
        </profile>

        <profile>
            <id>spring-5.2</id>
            <properties>
                <spring.framework.version>5.2.9.RELEASE</spring.framework.version>
            </properties>
        </profile>

    </profiles>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                    <fork>false</fork>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.2.0</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>