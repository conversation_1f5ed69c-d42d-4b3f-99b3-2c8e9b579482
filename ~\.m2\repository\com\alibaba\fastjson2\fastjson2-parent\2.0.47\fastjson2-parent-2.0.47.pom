<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.alibaba.fastjson2</groupId>
    <artifactId>fastjson2-parent</artifactId>
    <version>2.0.47</version>
    <name>${project.artifactId}</name>
    <description><PERSON>json is a JSON processor (JSON parser + JSON generator) written in Java</description>
    <packaging>pom</packaging>
    <url>https://github.com/alibaba/fastjson2</url>
    <inceptionYear>2022</inceptionYear>

    <licenses>
        <license>
            <name>Apache 2</name>
            <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution>
            <comments>A business-friendly OSS license</comments>
        </license>
    </licenses>
    <scm>
        <url>https://github.com/alibaba/fastjson2</url>
        <connection>scm:git:https://**************/alibaba/fastjson2.git</connection>
    </scm>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>${maven.compiler.source}</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <fastjson1x.version>1.2.83</fastjson1x.version>
        <jackson.version>2.16.1</jackson.version>
        <jetty.version>11.0.15</jetty.version>
        <jersey.version>2.41</jersey.version>
        <springframework5.version>5.3.31</springframework5.version>
        <springframework6.version>6.1.3</springframework6.version>
        <springsecurity5.version>5.8.8</springsecurity5.version>
        <springsecurity6.version>6.2.1</springsecurity6.version>
        <springboot2.version>2.7.18</springboot2.version>
        <springboot3.version>3.2.2</springboot3.version>
        <springdata2021.version>2021.2.18</springdata2021.version>
        <springdata2023.version>2023.1.0</springdata2023.version>
        <arrow.version>15.0.0</arrow.version>

        <!-- Requires kotlin minor version less than the latest version -->
        <kotlin.version>1.6.21</kotlin.version>
        <!-- https://kotlinlang.org/docs/maven.html#attributes-specific-to-jvm -->
        <kotlin.compiler.jvmTarget>1.8</kotlin.compiler.jvmTarget>
        <!-- https://kotlinlang.org/docs/maven.html#specifying-compiler-options -->
        <kotlin.compiler.apiVersion>1.5</kotlin.compiler.apiVersion>

        <junit5.version>5.10.2</junit5.version>
        <kotest.version>5.5.5</kotest.version>
        <jmh.version>1.37</jmh.version>

        <!-- overridden by submodule that need skip deploy -->
        <maven.deploy.skip>false</maven.deploy.skip>
    </properties>

    <modules>
        <!--
        <module>adapter</module>
        -->
        <module>benchmark</module>
        <!--
        <module>codegen</module>
        <module>codegen-test</module>
        -->
        <module>core</module>
        <!--
            <module>example-graalvm-native</module>
        -->
        <module>example-spring-test</module>
        <!--
            <module>example-spring6-test</module>
        -->
        <module>extension</module>
        <module>extension-spring5</module>
        <!--
        <module>extension-spring6</module>
        -->
        <module>fastjson1-compatible</module>
        <module>kotlin</module>
        <module>safemode-test</module>
    </modules>

    <organization>
        <name>Alibaba Group</name>
        <url>https://github.com/alibaba</url>
    </organization>
    <developers>
        <developer>
            <id>wenshao</id>
            <name>wenshao</name>
            <email>shaojin.wensj(at)alibaba-inc.com</email>
            <roles>
                <role>Developer</role>
                <role>Tech Leader</role>
            </roles>
            <timezone>+8</timezone>
            <url>https://github.com/wenshao</url>
        </developer>
        <developer>
            <id>oldratlee</id>
            <name>Jerry Lee</name>
            <email>oldratlee(at)gmail.com</email>
            <roles>
                <role>Developer</role>
                <role>CI/SCM Engineer</role>
            </roles>
            <timezone>+8</timezone>
            <url>https://github.com/oldratlee</url>
        </developer>
        <developer>
            <id>VictorZeng</id>
            <name>Victor Zeng</name>
            <email>Victor.Zxy(at)outlook.com</email>
            <roles>
                <role>Developer</role>
            </roles>
            <timezone>+8</timezone>
            <url>https://github.com/VictorZeng</url>
        </developer>
        <developer>
            <id>kraity</id>
            <name>陆之岇</name>
            <email>kat(at)krait.cn</email>
            <roles>
                <role>Developer</role>
            </roles>
            <timezone>+8</timezone>
            <url>https://github.com/kraity</url>
        </developer>
    </developers>

    <distributionManagement>
        <repository>
            <id>ossrh</id>
            <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
        </repository>
        <snapshotRepository>
            <id>ossrh</id>
            <url>https://oss.sonatype.org/content/repositories/snapshots</url>
        </snapshotRepository>
    </distributionManagement>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson1x.version}</version>
            </dependency>
            <dependency>
                <groupId>cglib</groupId>
                <artifactId>cglib-nodep</artifactId>
                <version>3.3.0</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>5.8.26</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun.odps</groupId>
                <artifactId>odps-sdk-core</artifactId>
                <version>0.45.6-public</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun.odps</groupId>
                <artifactId>odps-sdk-udf</artifactId>
                <version>0.45.6-public</version>
            </dependency>
            <dependency>
                <groupId>com.carrotsearch</groupId>
                <artifactId>hppc</artifactId>
                <version>0.9.1</version>
            </dependency>
            <dependency>
                <groupId>com.caucho</groupId>
                <artifactId>hessian</artifactId>
                <version>4.0.66</version>
            </dependency>
            <dependency>
                <groupId>com.chinamobile.cmos</groupId>
                <artifactId>sms-core</artifactId>
                <version>2.1.13.4</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson</groupId>
                <artifactId>jackson-bom</artifactId>
                <type>pom</type>
                <version>${jackson.version}</version>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.github.erosb</groupId>
                <artifactId>everit-json-schema</artifactId>
                <version>1.14.4</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>2.10.1</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>33.0.0-jre</version>
            </dependency>
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-bom</artifactId>
                <type>pom</type>
                <version>3.25.3</version>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.googlecode.json-simple</groupId>
                <artifactId>json-simple</artifactId>
                <version>1.1.1</version>
            </dependency>
            <dependency>
                <groupId>com.jayway.jsonpath</groupId>
                <artifactId>json-path</artifactId>
                <version>2.9.0</version>
            </dependency>
            <dependency>
                <groupId>com.jsoniter</groupId>
                <artifactId>jsoniter</artifactId>
                <version>0.9.23</version>
            </dependency>
            <dependency>
                <groupId>com.networknt</groupId>
                <artifactId>json-schema-validator</artifactId>
                <version>1.0.88</version>
            </dependency>
            <dependency>
                <groupId>com.opencsv</groupId>
                <artifactId>opencsv</artifactId>
                <version>5.9</version>
            </dependency>
            <dependency>
                <groupId>com.oracle.ojdbc</groupId>
                <artifactId>ojdbc8</artifactId>
                <version>19.3.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.retrofit2</groupId>
                <artifactId>retrofit</artifactId>
                <version>2.9.0</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>2.15.1</version>
            </dependency>
            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>2.6</version>
            </dependency>
            <dependency>
                <groupId>io.airlift</groupId>
                <artifactId>slice</artifactId>
                <!-- DO NOT upgrade to 0.42+, since don't support java 8 -->
                <version>0.41</version>
            </dependency>

            <!-- https://github.com/kotlintest/kotlintest -->
            <dependency>
                <groupId>io.kotest</groupId>
                <artifactId>kotest-assertions-core-jvm</artifactId>
                <version>${kotest.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>io.kotest</groupId>
                <artifactId>kotest-property-jvm</artifactId>
                <version>${kotest.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>io.kotest</groupId>
                <artifactId>kotest-runner-junit5-jvm</artifactId>
                <version>${kotest.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>io.projectreactor</groupId>
                <artifactId>reactor-core</artifactId>
                <version>3.6.2</version>
            </dependency>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-spring-web</artifactId>
                <version>3.0.0</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>javax.money</groupId>
                <artifactId>money-api</artifactId>
                <version>1.1</version>
            </dependency>
            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>javax.servlet-api</artifactId>
                <version>4.0.1</version>
            </dependency>

            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>2.12.7</version>
            </dependency>


            <!--
               various json/serialization libs.
               for comparison test by test code
           -->

            <dependency>
                <groupId>net.minidev</groupId>
                <artifactId>json-smart</artifactId>
                <version>2.5.0</version>
            </dependency>
            <dependency>
                <groupId>net.sf.json-lib</groupId>
                <artifactId>json-lib</artifactId>
                <version>2.4</version>
                <classifier>jdk15</classifier>
            </dependency>
            <dependency>
                <groupId>net.sf.trove4j</groupId>
                <artifactId>trove4j</artifactId>
                <version>3.0.3</version>
            </dependency>

            <dependency>
                <groupId>org.apache.arrow</groupId>
                <artifactId>arrow-memory-netty</artifactId>
                <version>${arrow.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.arrow</groupId>
                <artifactId>arrow-vector</artifactId>
                <version>${arrow.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>4.4</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.14.0</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-bom</artifactId>
                <type>pom</type>
                <version>${jetty.version}</version>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.gitlab4j</groupId>
                <artifactId>gitlab4j-api</artifactId>
                <version>5.5.0</version>
            </dependency>

            <dependency>
                <groupId>org.glassfish.jersey</groupId>
                <artifactId>jersey-bom</artifactId>
                <type>pom</type>
                <version>${jersey.version}</version>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.hamcrest</groupId>
                <artifactId>hamcrest</artifactId>
                <version>2.2</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-core</artifactId>
                <version>5.6.15.Final</version>
            </dependency>

            <dependency>
                <groupId>org.javamoney</groupId>
                <artifactId>moneta</artifactId>
                <version>1.1</version>
            </dependency>
            <dependency>
                <groupId>org.javamoney.moneta</groupId>
                <artifactId>moneta-core</artifactId>
                <version>1.4.4</version>
            </dependency>

            <dependency>
                <groupId>org.javassist</groupId>
                <artifactId>javassist</artifactId>
                <version>3.30.2-GA</version>
            </dependency>

            <dependency>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-bom</artifactId>
                <type>pom</type>
                <version>${kotlin.version}</version>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.json</groupId>
                <artifactId>json</artifactId>
                <version>20240205</version>
            </dependency>

            <dependency>
                <groupId>org.junit</groupId>
                <artifactId>junit-bom</artifactId>
                <type>pom</type>
                <version>${junit5.version}</version>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-all</artifactId>
                <version>1.10.19</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.msgpack</groupId>
                <artifactId>msgpack-core</artifactId>
                <version>0.9.8</version>
            </dependency>

            <dependency>
                <groupId>org.openjdk.jmh</groupId>
                <artifactId>jmh-core</artifactId>
                <version>${jmh.version}</version>
            </dependency>
            <dependency>
                <groupId>org.openjdk.jmh</groupId>
                <artifactId>jmh-generator-annprocess</artifactId>
                <version>${jmh.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.30</version>
                <scope>test</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>io.kotest</groupId>
            <artifactId>kotest-assertions-core-jvm</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.kotest</groupId>
            <artifactId>kotest-property-jvm</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.kotest</groupId>
            <artifactId>kotest-runner-junit5-jvm</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-all</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>test</scope>
        </dependency>


    </dependencies>

    <build>
        <plugins>
            <!--
                Compiling Kotlin and Java sources - Using Maven - Kotlin Programming Language
                https://kotlinlang.org/docs/reference/using-maven.html#compiling-kotlin-and-java-sources

                Making sure that the kotlin plugin is above the maven-compiler-plugin in your pom.xml file!
            -->
            <plugin>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-maven-plugin</artifactId>
                <version>${kotlin.version}</version>
                <executions>
                    <execution>
                        <id>compile</id>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                        <configuration>
                            <sourceDirs>
                                <sourceDir>${project.basedir}/src/main/java</sourceDir>
                            </sourceDirs>
                        </configuration>
                    </execution>
                    <execution>
                        <id>test-compile</id>
                        <goals>
                            <goal>test-compile</goal>
                        </goals>
                        <configuration>
                            <sourceDirs>
                                <sourceDir>${project.basedir}/src/test/java</sourceDir>
                            </sourceDirs>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <executions>
                    <!-- Replacing default-compile as it is treated specially by maven -->
                    <execution>
                        <id>default-compile</id>
                        <phase>none</phase>
                    </execution>
                    <!-- Replacing default-testCompile as it is treated specially by maven -->
                    <execution>
                        <id>default-testCompile</id>
                        <phase>none</phase>
                    </execution>
                    <execution>
                        <id>java-compile</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>java-test-compile</id>
                        <phase>test-compile</phase>
                        <goals>
                            <goal>testCompile</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>3.3.1</version>
                <dependencies>
                    <dependency>
                        <groupId>com.puppycrawl.tools</groupId>
                        <artifactId>checkstyle</artifactId>
                        <version>9.2</version>
                    </dependency>
                </dependencies>
                <executions>
                    <execution>
                        <id>checkstyle</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>check</goal>
                        </goals>
                        <configuration>
                            <skip>false</skip>
                            <failOnViolation>true</failOnViolation>
                            <includeTestSourceDirectory>true</includeTestSourceDirectory>
                            <configLocation>src/checkstyle/fastjson2-checks.xml</configLocation>
                            <excludes>module-info.java,
                                com/alibaba/fastjson2/fieldbased/Case1.java,
                                com/alibaba/fastjson2/codec/UnicodeClassNameTest.java,
                                com/alibaba/fastjson2/hsf/UCaseNameTest.java,
                                com/alibaba/json/bvtVO/PushMsg.java,
                                com/alibaba/json/bvtVO/IncomingDataPoint_ext_double.java,
                                com/alibaba/json/bvtVO/vip_com/QueryLoanOrderRsp.java,
                                com/alibaba/json/bvtVO/vip_com/TxnListItsm.java,
                                com/alibaba/json/bvtVO/一个中文名字的包/User.java,
                                com/alibaba/fastjson2_vo/cart/OrderBy.java,com/alibaba/fastjson2_vo/homepage/GetHomePageData.java,
                                com/alibaba/fastjson2/v1issues/issue_4000/Issue4073.java,
                                com/alibaba/fastjson2/v1issues/issue_1400/Issue1486.java,
                                com/alibaba/fastjson2/v1issues/issue_1400/Issue1487.java,
                                com/alibaba/fastjson2/v1issues/issue_1300/Issue1335.java,
                                com/alibaba/fastjson2/v1issues/issue_1200/Issue1254.java,
                                com/alibaba/fastjson2/v1issues/issue_1200/Issue1276.java,
                                com/alibaba/fastjson2/v1issues/issue_1100/Issue1165.java,
                                com/alibaba/fastjson2/v1issues/issue_1500/Issue1576.java,
                                com/alibaba/fastjson2/v1issues/issue_1500/Issue1529.java,
                                com/alibaba/fastjson2/issues/Issue371.java,
                                com/alibaba/fastjson2/issues/Issue325.java,
                                com/alibaba/fastjson2/issues/Issue402.java,
                                com/alibaba/fastjson2/v1issues/issue_1500/Issue1558.java,
                                com/alibaba/fastjson/issue_2800/Issue2866.java,
                                com/alibaba/fastjson/issues_compatible/Issue325.java,
                                com/alibaba/fastjson/issue_3200/Issue3217.java,
                                com/alibaba/fastjson/issue_2400/Issue2488.java,
                                com/alibaba/fastjson/issue_1100/Issue1165.java,
                                com/alibaba/fastjson/issue_1200/Issue1254.java,
                                com/alibaba/fastjson/issue_1200/Issue1276.java,
                                com/alibaba/fastjson/issue_1300/Issue1306.java,
                                com/alibaba/fastjson/issue_1300/Issue1335.java,
                                com/alibaba/fastjson/issue_1300/Issue1367.java,
                                com/alibaba/fastjson/issue_1400/Issue1486.java,
                                com/alibaba/fastjson/issue_1400/Issue1487.java,
                                com/alibaba/fastjson/issue_1500/Issue1576.java,
                                com/alibaba/fastjson/issue_1500/Issue1529.java,
                                com/alibaba/fastjson/issue_1500/Issue1558.java,
                                com/alibaba/fastjson/issue_1600/Issue*.java,
                                com/alibaba/fastjson/issue_1700/Issue*.java,
                                com/alibaba/fastjson/issue_1900/Issue*.java,
                                com/alibaba/fastjson/issue_2300/Issue*.java,
                                com/alibaba/fastjson2/naming/*.java,
                                com/alibaba/json/bvt/**/*.java,
                                com/alibaba/fastjson2/issues/Issue485.java,
                                com/alibaba/fastjson2/issues/Issue765.java,
                                com/alibaba/fastjson2/issues/Issue924.java,
                                com/alibaba/fastjson2/issues_1000/Issue1084.java,
                                com/alibaba/fastjson/issue_1300/Issue1367_jaxrs.java,
                                com/fasterxml/jackson/core/*.java,
                                com/fasterxml/jackson/databind/*.java,
                                com/alibaba/fastjson2/adapter/http/*.java,
                                com/alibaba/fastjson2/dubbo/GoogleProtobufBasic.java,
                                com/alibaba/fastjson2/issues/Issue983.java,
                                com/alibaba/fastjson2/protobuf/PersonOuterClass.java,
                                com/alibaba/fastjson2/benchmark/protobuf/MediaContentHolder.java,
                                org/apache/dubbo/springboot/demo/BusinessException.java,
                                com/alibaba/fastjson2/issues_1000/Issue1246.java,
                                com/alibaba/fastjson/issues_compatible/Issue1303.java,
                                com/alibaba/fastjson2/util/BeanUtilsTest.java,
                                com/alibaba/fastjson2/issues_1000/Issue1395.java,
                                com/alibaba/fastjson2/issues_2100/Issue2164.java,
                                com/alibaba/fastjson2/issues_2100/Issue2181.java,
                                com/alibaba/fastjson/v2issues/Issue1432.java
                            </excludes>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.gaul</groupId>
                <artifactId>modernizer-maven-plugin</artifactId>
                <version>2.7.0</version>
                <configuration>
                    <violationsFiles>
                        <violationsFile>${maven.multiModuleProjectDirectory}/src/violations.xml</violationsFile>
                    </violationsFiles>
                </configuration>
            </plugin>

            <!--
                official docs: https://maven.apache.org/enforcer/enforcer-rules/requireMavenVersion.html

                add maven-enforce-plugin to make sure the right jdk is used
                https://stackoverflow.com/a/18420462/922688
            -->
            <plugin>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>3.4.1</version>
                <executions>
                    <execution>
                        <id>enforce-maven</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <requireMavenVersion>
                                    <version>3.3.9</version>
                                </requireMavenVersion>
                                <requireJavaVersion>
                                    <version>[1.8,)</version>
                                </requireJavaVersion>
                                <compound
                                        implementation="com.github.ferstl.maven.pomenforcers.CompoundPedanticEnforcer">
                                    <enforcers>
                                        POM_SECTION_ORDER,MODULE_ORDER,DEPENDENCY_MANAGEMENT_ORDER,DEPENDENCY_ORDER,DEPENDENCY_ELEMENT
                                    </enforcers>
                                    <pomSectionPriorities>
                                        modelVersion,parent,groupId,artifactId,version,name,description,packaging,url,inceptionYear,licenses,scm,properties,modules
                                    </pomSectionPriorities>
                                    <dependenciesGroupIdPriorities>com.alibaba.fastjson2,com.alibaba
                                    </dependenciesGroupIdPriorities>
                                    <dependenciesOrderBy>scope,groupId,artifactId</dependenciesOrderBy>
                                    <dependenciesScopePriorities>compile,runtime,provided,test
                                    </dependenciesScopePriorities>
                                    <dependencyManagementOrderBy>groupId,artifactId</dependencyManagementOrderBy>
                                    <dependencyManagementGroupIdPriorities>com.alibaba.fastjson2,com.alibaba
                                    </dependencyManagementGroupIdPriorities>
                                    <dependencyElementOrdering>groupId,artifactId,type,version
                                    </dependencyElementOrdering>
                                </compound>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>com.github.ferstl</groupId>
                        <artifactId>pedantic-pom-enforcers</artifactId>
                        <version>2.2.0</version>
                    </dependency>
                </dependencies>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <artifactId>maven-clean-plugin</artifactId>
                    <version>3.3.2</version>
                </plugin>
                <plugin>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>3.3.1</version>
                </plugin>
                <plugin>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>3.3.0</version>
                </plugin>
                <plugin>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.3.0</version>
                </plugin>
                <plugin>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.12.1</version>
                </plugin>
                <plugin>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>3.6.3</version>
                </plugin>
                <plugin>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>3.2.5</version>
                </plugin>
                <plugin>
                    <artifactId>maven-assembly-plugin</artifactId>
                    <version>3.6.0</version>
                </plugin>
                <plugin>
                    <artifactId>maven-site-plugin</artifactId>
                    <version>3.12.1</version>
                </plugin>
                <plugin>
                    <artifactId>maven-gpg-plugin</artifactId>
                    <version>3.0.1</version>
                </plugin>
                <plugin>
                    <artifactId>maven-install-plugin</artifactId>
                    <version>3.1.1</version>
                </plugin>
                <plugin>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>3.1.1</version>
                </plugin>
                <plugin>
                    <artifactId>maven-shade-plugin</artifactId>
                    <version>3.5.2</version>
                </plugin>
                <plugin>
                    <groupId>org.sonatype.plugins</groupId>
                    <artifactId>nexus-staging-maven-plugin</artifactId>
                    <version>1.6.13</version>
                </plugin>
                <plugin>
                    <!-- https://github.com/moditect/moditect -->
                    <groupId>org.moditect</groupId>
                    <artifactId>moditect-maven-plugin</artifactId>
                    <version>1.1.0</version>
                    <executions>
                        <execution>
                            <id>add-module-infos</id>
                            <phase>package</phase>
                            <goals>
                                <goal>add-module-info</goal>
                            </goals>
                            <configuration>
                                <jvmVersion>9</jvmVersion>
                                <overwriteExistingFiles>true</overwriteExistingFiles>
                                <module>
                                    <!-- using `moduleInfoFile` is more IDE/editor friendly than `moduleInfoSource`/`moduleInfo` -->
                                    <moduleInfoFile>
                                        ${project.basedir}/src/main/moditect/module-info.java
                                    </moduleInfoFile>
                                </module>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <profiles>
        <profile>
            <id>gen-src</id>
            <activation>
                <property>
                    <name>performRelease</name>
                    <value>true</value>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <artifactId>maven-source-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>attach-sources</id>
                                <goals>
                                    <goal>jar-no-fork</goal>
                                </goals>
                            </execution>
                        </executions>
                        <configuration>
                            <attach>true</attach>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>gen-javadoc</id>
            <activation>
                <property>
                    <name>performRelease</name>
                    <value>true</value>
                </property>
            </activation>
            <properties>
                <javadoc.default.exclude.packages>
                    *.internal:*.internal.*:*.internal.*.*:*.internal.*.*.*:*.internal.*.*.*.*
                </javadoc.default.exclude.packages>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <artifactId>maven-javadoc-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>attach-javadoc</id>
                                <goals>
                                    <goal>jar</goal>
                                </goals>
                            </execution>
                        </executions>
                        <configuration>
                            <show>protected</show>
                            <charset>UTF-8</charset>
                            <encoding>UTF-8</encoding>
                            <docencoding>UTF-8</docencoding>
                            <!-- https://maven.apache.org/plugins/maven-javadoc-plugin/examples/exclude-package-names.html -->
                            <excludePackageNames>
                                ${javadoc.default.exclude.packages}:jdk.incubator.vector
                            </excludePackageNames>
                            <doclint>all,-missing</doclint>
                            <links>
                                <link>https://docs.oracle.com/javase/8/docs/api</link>
                            </links>
                            <additionalJOptions>
                                <additionalJOption>-J-Duser.language=en -J-Duser.country=US</additionalJOption>
                            </additionalJOptions>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>gen-sign</id>
            <activation>
                <property>
                    <name>performRelease</name>
                    <value>true</value>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>gen-git-properties</id>
            <activation>
                <property>
                    <name>performRelease</name>
                    <value>true</value>
                </property>
            </activation>
            <build>
                <plugins>
                    <!--
                        Maven plugin which includes build-time git repository information into an POJO / *.properties).
                        Make your apps tell you which version exactly they were built from! Priceless in large distributed deployments.
                            https://github.com/git-commit-id/git-commit-id-maven-plugin
                    -->
                    <plugin>
                        <groupId>io.github.git-commit-id</groupId>
                        <artifactId>git-commit-id-maven-plugin</artifactId>
                        <!-- DO NOT upgrade to v5+, since don't support java 8 -->
                        <version>7.0.0</version>
                        <executions>
                            <execution>
                                <id>get-the-git-infos</id>
                                <goals>
                                    <goal>revision</goal>
                                </goals>
                            </execution>
                            <execution>
                                <id>validate-the-git-infos</id>
                                <goals>
                                    <goal>validateRevision</goal>
                                </goals>
                            </execution>
                        </executions>
                        <configuration>
                            <validationProperties>
                                <!-- verify that the current repository is not dirty -->
                                <validationProperty>
                                    <name>validating git dirty</name>
                                    <value>${git.dirty}</value>
                                    <shouldMatchTo>false</shouldMatchTo>
                                </validationProperty>
                            </validationProperties>
                            <generateGitPropertiesFile>true</generateGitPropertiesFile>
                            <generateGitPropertiesFilename>
                                ${project.build.outputDirectory}/META-INF/scm/${project.groupId}/${project.artifactId}/git.properties
                            </generateGitPropertiesFilename>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>gen-code-cov</id>
            <activation>
                <property>
                    <!-- https://docs.github.com/en/actions/learn-github-actions/variables#default-environment-variables -->
                    <name>env.CI</name>
                    <value>true</value>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <!--
                            Code Coverage
                            config example: https://github.com/codecov/example-java
                            plugin docs: https://eclemma.org/jacoco/trunk/doc/
                        -->
                        <groupId>org.jacoco</groupId>
                        <artifactId>jacoco-maven-plugin</artifactId>
                        <version>0.8.11</version>
                        <configuration>
                            <excludes>
                                <exclude>com/alibaba/fastjson2/benchmark</exclude>
                                <exclude>com/alibaba/example/springtest</exclude>
                                <exclude>com/alibaba/example/spring6test</exclude>
                            </excludes>
                        </configuration>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>prepare-agent</goal>
                                </goals>
                            </execution>
                            <execution>
                                <id>report</id>
                                <phase>test</phase>
                                <goals>
                                    <goal>report</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>enable-incubators-for-jdk17+</id>
            <activation>
                <jdk>[17,)</jdk>
            </activation>
            <modules>
                <!--
                <module>example-graalvm-native</module>
                -->
                <module>extension-spring6</module>
                <module>example-spring6-test</module>
                <module>incubator-vector</module>
                <module>test-jdk17</module>
            </modules>
        </profile>
        <profile>
            <id>deploy-settings</id>
            <activation>
                <property>
                    <name>performRelease</name>
                    <value>true</value>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.sonatype.plugins</groupId>
                        <artifactId>nexus-staging-maven-plugin</artifactId>
                        <extensions>true</extensions>
                        <!--
                            In multi-module builds using the deploy-at-end feature,
                            the deployment of all components is performed in the last module based on the reactor order.
                            If this property is set to true in the last module,
                            all staging deployment for all modules will be skipped.
                            so, we'll config nexus deploy after deploy phase of every module
                            see: https://github.com/sonatype/nexus-maven-plugins/tree/master/staging/maven-plugin#configuring-the-plugin
                        -->
                        <executions>
                            <execution>
                                <id>default-deploy</id>
                                <phase>deploy</phase>
                                <goals>
                                    <goal>deploy</goal>
                                </goals>
                            </execution>
                        </executions>
                        <configuration>
                            <serverId>ossrh</serverId>
                            <nexusUrl>https://oss.sonatype.org/</nexusUrl>
                            <autoReleaseAfterClose>true</autoReleaseAfterClose>
                            <!--
                            If you are deploying to Maven Central,
                            it is the Nexus Staging Plugin that is doing the deployment instead of the `deploy` plugin,
                            so the configuration of the `deploy` plugin has no effect.
                            To make the Nexus deploy plugin skip,
                            set skipNexusStagingDeployMojo in its configuration to true.
                            see: https://stackoverflow.com/questions/59552549/preventing-maven-modules-from-being-deployed
                        -->
                            <skipNexusStagingDeployMojo>${maven.deploy.skip}</skipNexusStagingDeployMojo>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
