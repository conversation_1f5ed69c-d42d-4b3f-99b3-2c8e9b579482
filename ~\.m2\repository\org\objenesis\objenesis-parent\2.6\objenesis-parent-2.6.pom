<?xml version="1.0" encoding="ISO-8859-1"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.objenesis</groupId>
  <artifactId>objenesis-parent</artifactId>
  <version>2.6</version>
  <packaging>pom</packaging>

  <name>Objenesis parent project</name>
  <description>A library for instantiating Java objects</description>
  <url>http://objenesis.org</url>
  <inceptionYear>2006</inceptionYear>

  <prerequisites>
    <maven>3.2.1</maven>
  </prerequisites>

  <modules>
    <module>main</module>
    <module>tck</module>
  </modules>

  <licenses>
    <license>
      <name>Apache 2</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <!-- Used for the copyright -->
  <organization>
    <name>Joe Walnes, Henri Tremblay, Leonardo Mesquita</name>
  </organization>

  <scm>
    <url>https://github.com/easymock/objenesis</url>
    <developerConnection>scm:git:**************:easymock/objenesis.git</developerConnection>
    <connection>scm:git:https://github.com/easymock/objenesis.git</connection>
    <tag>2.6</tag>
  </scm>

  <developers>
    <developer>
      <id>joe</id>
      <name>Joe Walnes</name>
      <timezone>-5</timezone>
    </developer>
    <developer>
      <id>henri</id>
      <name>Henri Tremblay</name>
      <timezone>-5</timezone>
    </developer>
    <developer>
      <id>leonardo</id>
      <name>Leonardo Mesquita</name>
      <timezone>-5</timezone>
    </developer>
  </developers>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <version>4.12</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <source>1.6</source>
          <target>1.6</target>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <configuration>
          <archive>
            <index>true</index>
            <addMavenDescriptor>false</addMavenDescriptor>
            <manifest>
              <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
              <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
            </manifest>
          </archive>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-release-plugin</artifactId>
        <configuration>
          <!-- Required to let release with the benchmark project using snapshots. I can't put it directly in the benchmark project. It doesn't work -->
          <allowTimestampedSnapshots>true</allowTimestampedSnapshots>
          <!-- Renamed because the default would be objenesis-parent-x.y -->
          <tagNameFormat>@{project.version}</tagNameFormat>
          <!-- Quite annoying in case of error that the changes were pushed -->
          <pushChanges>false</pushChanges>
          <!-- Do not activate special profile from the superpom. We do it ourselves -->
          <useReleaseProfile>false</useReleaseProfile>
          <!-- Profiles required for the release -->
          <releaseProfiles>release,full,all</releaseProfiles>
          <!-- To use the same version on the module and the parent -->
          <autoVersionSubmodules>true</autoVersionSubmodules>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-site-plugin</artifactId>
        <inherited>false</inherited>
        <configuration>
          <siteDirectory>${project.basedir}/website</siteDirectory>
        </configuration>
      </plugin>
      <plugin>
        <groupId>com.mycila.maven-license-plugin</groupId>
        <artifactId>maven-license-plugin</artifactId>
        <inherited>false</inherited>
        <configuration>
          <!-- skipping the license check on the parent pom since it's not needed -->
          <skip>true</skip>
        </configuration>
      </plugin>
    </plugins>
    <extensions>
      <extension>
        <groupId>org.apache.maven.wagon</groupId>
        <artifactId>wagon-ssh-external</artifactId>
        <version>2.10</version>
      </extension>
    </extensions>
    <pluginManagement>
      <plugins>
        <plugin>
          <artifactId>maven-assembly-plugin</artifactId>
          <version>3.0.0</version>
        </plugin>
        <plugin>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>3.6.1</version>
        </plugin>
        <plugin>
          <artifactId>maven-jar-plugin</artifactId>
          <version>3.0.2</version>
        </plugin>
        <plugin>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>2.19.1</version>
        </plugin>
        <plugin>
          <artifactId>maven-clean-plugin</artifactId>
          <version>3.0.0</version>
        </plugin>
        <plugin>
          <artifactId>maven-deploy-plugin</artifactId>
          <version>2.8.2</version>
        </plugin>
        <plugin>
          <artifactId>maven-gpg-plugin</artifactId>
          <version>1.6</version>
          <executions>
            <execution>
              <id>sign-artifacts</id>
              <phase>verify</phase>
              <goals>
                <goal>sign</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <artifactId>maven-install-plugin</artifactId>
          <version>2.5.2</version>
        </plugin>
        <plugin>
          <artifactId>maven-release-plugin</artifactId>
          <version>2.5.3</version>
        </plugin>
        <plugin>
          <artifactId>maven-resources-plugin</artifactId>
          <version>3.0.1</version>
        </plugin>
        <plugin>
          <artifactId>maven-shade-plugin</artifactId>
          <version>2.4.3</version>
        </plugin>
        <plugin>
          <artifactId>maven-site-plugin</artifactId>
          <version>3.5.1</version>
        </plugin>
        <plugin>
          <artifactId>maven-source-plugin</artifactId>
          <version>3.0.1</version>
        </plugin>
        <plugin>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>2.10.4</version>
        </plugin>
        <plugin>
          <groupId>org.apache.felix</groupId>
          <artifactId>maven-bundle-plugin</artifactId>
          <version>3.2.0</version>
        </plugin>
        <plugin>
          <groupId>com.keyboardsamurais.maven</groupId>
          <artifactId>maven-timestamp-plugin</artifactId>
          <version>1.0</version>
          <executions>
            <execution>
              <id>year</id>
              <goals>
                <goal>create</goal>
              </goals>
              <configuration>
                <propertyName>year</propertyName>
                <timestampPattern>yyyy</timestampPattern>
              </configuration>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>com.mycila.maven-license-plugin</groupId>
          <artifactId>maven-license-plugin</artifactId>
          <version>1.10.b1</version>
          <configuration>
            <header>${project.basedir}/../header.txt</header>
            <strictCheck>true</strictCheck>
            <excludes>
              <!-- Not being ignore by default -->
              <exclude>.gitignore</exclude>
              <!-- Nothing is copyrighted in target -->
              <exclude>target/**</exclude>
              <!-- generated pom by maven-shade-plugin -->
              <exclude>dependency-reduced-pom.xml</exclude>
              <!-- generated by Equinox during OSGi test -->
              <exclude>eclipse_config/**</exclude>
              <!-- no header wanted on the website -->
              <exclude>website/**</exclude>
              <!-- no header on batch files -->
              <exclude>**/*.bat</exclude>
              <!-- generated Android files -->
              <exclude>project.properties</exclude>
              <exclude>lint.xml</exclude>
              <exclude>gen/**</exclude>
              <exclude>bin/**</exclude>
              <!-- no header on text files -->
              <exclude>**/*.txt</exclude>
              <!-- no header on Eclipse launch files -->
              <exclude>**/*.launch</exclude>
              <!-- no header on markdown files -->
              <exclude>**/*.md</exclude>
            </excludes>
            <properties>
              <inceptionYear>${project.inceptionYear}</inceptionYear>
              <year>${year}</year>
            </properties>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-remote-resources-plugin</artifactId>
          <version>1.5</version>
          <executions>
            <execution>
              <goals>
                <goal>process</goal>
              </goals>
              <configuration>
                <resourceBundles>
                  <resourceBundle>org.apache:apache-jar-resource-bundle:1.3</resourceBundle>
                </resourceBundles>
              </configuration>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>exec-maven-plugin</artifactId>
          <version>1.5.0</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>versions-maven-plugin</artifactId>
          <version>2.4</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>findbugs-maven-plugin</artifactId>
          <version>3.0.4</version>
          <configuration>
            <xmlOutput>true</xmlOutput>
            <omitVisitors>Naming</omitVisitors>
          </configuration>
        </plugin>
        <!--This plugin configuration is used to store Eclipse m2e settings only. It has no influence on the Maven build itself. -->
        <plugin>
          <groupId>org.eclipse.m2e</groupId>
          <artifactId>lifecycle-mapping</artifactId>
          <version>1.0.0</version>
          <configuration>
            <lifecycleMappingMetadata>
              <pluginExecutions>
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>com.keyboardsamurais.maven</groupId>
                    <artifactId>maven-timestamp-plugin</artifactId>
                    <versionRange>[1.0,)</versionRange>
                    <goals>
                      <goal>create</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <execute />
                  </action>
                </pluginExecution>
                <pluginExecution>
                  <pluginExecutionFilter>
                    <artifactId>maven-remote-resources-plugin</artifactId>
                    <versionRange>[1.0,)</versionRange>
                    <goals>
                      <goal>process</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <execute />
                  </action>
                </pluginExecution>
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>findbugs-maven-plugin</artifactId>
                    <versionRange>[2.5.5,)</versionRange>
                    <goals>
                      <goal>findbugs</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <ignore />
                  </action>
                </pluginExecution>
              </pluginExecutions>
            </lifecycleMappingMetadata>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>
  <reporting>
    <plugins>
      <plugin>
        <artifactId>maven-project-info-reports-plugin</artifactId>
        <version>2.9</version>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>findbugs-maven-plugin</artifactId>
        <version>3.0.4</version>
      </plugin>
      <plugin>
        <artifactId>maven-pmd-plugin</artifactId>
        <version>3.7</version>
        <configuration>
          <targetJdk>1.5</targetJdk>
        </configuration>
      </plugin>
    </plugins>
  </reporting>

  <distributionManagement>
    <repository>
      <id>bintray</id>
      <name>JFrog Bintray</name>
      <url>https://api.bintray.com/maven/easymock/maven/objenesis/;publish=1</url>
    </repository>
  </distributionManagement>

  <profiles>
    <profile>
      <!-- Activate to generate javadoc, sources jars, license check and findbugs -->
      <id>full</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-source-plugin</artifactId>
            <executions>
              <execution>
                <id>attach-sources</id>
                <goals>
                  <goal>jar</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <artifactId>maven-javadoc-plugin</artifactId>
            <executions>
              <execution>
                <id>attach-javadocs</id>
                <goals>
                  <goal>jar</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>findbugs-maven-plugin</artifactId>
            <executions>
              <execution>
                <id>findbugs</id>
                <goals>
                  <goal>findbugs</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>com.mycila.maven-license-plugin</groupId>
            <artifactId>maven-license-plugin</artifactId>
            <executions>
              <execution>
                <id>check</id>
                <goals>
                  <goal>check</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <!-- Activate to update the license -->
      <id>license</id>
      <build>
        <pluginManagement>
          <plugins>
            <plugin>
              <groupId>com.mycila.maven-license-plugin</groupId>
              <artifactId>maven-license-plugin</artifactId>
              <executions>
                <execution>
                  <id>format</id>
                  <phase>generate-sources</phase>
                  <goals>
                    <goal>format</goal>
                  </goals>
                </execution>
              </executions>
            </plugin>
          </plugins>
        </pluginManagement>
      </build>
    </profile>
    <profile>
      <!-- Activate to generate the website -->
      <id>website</id>
      <modules>
        <module>website</module>
      </modules>
    </profile>
    <profile>
      <!-- Activate to execute the tck on Android -->
      <id>android</id>
      <modules>
        <module>tck-android</module>
      </modules>
    </profile>
    <profile>
      <!-- Activate to run the benchmark -->
      <id>benchmark</id>
      <modules>
        <module>benchmark</module>
      </modules>
    </profile>
    <profile>
      <!-- Activate to create a complete release -->
      <id>release</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-gpg-plugin</artifactId>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>all</id>
      <modules>
        <module>benchmark</module>
        <module>tck-android</module>
        <module>gae</module>
        <module>website</module>
      </modules>
    </profile>
  </profiles>
</project>
