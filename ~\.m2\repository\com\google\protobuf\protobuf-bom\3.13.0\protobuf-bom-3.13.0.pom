<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0                              http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>com.google.protobuf</groupId>
  <artifactId>protobuf-bom</artifactId>
  <version>3.13.0</version>
  <packaging>pom</packaging>

  <name>Protocol Buffers [BOM]</name>
  <description>A compatible set of open source libraries for working with protocol buffers.</description>
  <url>https://developers.google.com/protocol-buffers/</url>

  <organization>
    <name>Google LLC</name>
    <url>https://cloud.google.com</url>
  </organization>

  <developers>
    <developer>
      <id>haon</id>
      <name><PERSON><PERSON></name>
      <email><EMAIL></email>
      <organization>Google</organization>
      <organizationUrl>https://cloud.google.com</organizationUrl>
      <timezone>America/Los_Angeles</timezone>
    </developer>
  </developers>

  <licenses>
    <license>
      <name>3-Clause BSD License</name>
      <url>https://opensource.org/licenses/BSD-3-Clause</url>
    </license>
  </licenses>

  <scm>
    <url>https://github.com/protocolbuffers/protobuf</url>
    <connection>scm:git:https://github.com/protocolbuffers/protobuf.git</connection>
  </scm>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>

  <distributionManagement>
    <snapshotRepository>
      <id>sonatype-nexus-staging</id>
      <url>https://oss.sonatype.org/content/repositories/snapshots</url>
    </snapshotRepository>
    <repository>
      <id>sonatype-nexus-staging</id>
      <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
  </distributionManagement>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.google.protobuf</groupId>
        <artifactId>protobuf-java</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.protobuf</groupId>
        <artifactId>protobuf-java-util</artifactId>
        <version>${project.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <profiles>
    <profile>
      <!-- If you see the error message
           gpg: signing failed: Inappropriate ioctl for device
           when signing run the command
           export GPG_TTY=$(tty)
           and try again. -->
      <id>release</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-gpg-plugin</artifactId>
            <version>1.6</version>
            <executions>
              <execution>
                <id>sign-artifacts</id>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
               </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.sonatype.plugins</groupId>
	    <artifactId>nexus-staging-maven-plugin</artifactId>
            <version>1.6.6</version>
            <extensions>true</extensions>
            <configuration>
              <nexusUrl>https://oss.sonatype.org/</nexusUrl>
              <serverId>sonatype-nexus-staging</serverId>
              <autoReleaseAfterClose>false</autoReleaseAfterClose>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>
